<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Thème Workeem - Aperçu</title>
    <link rel="stylesheet" href="workeem/login/resources/css/workeem.css">
    <style>
        /* Simulation de la structure Keycloak pour le test */
        .test-container {
            min-height: 100vh;
            background: var(--workeem-background);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
        }
        
        .test-card {
            background: var(--workeem-card-bg);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: var(--workeem-shadow);
            border: 1px solid rgba(255, 255, 255, 0.3);
            max-width: 420px;
            width: 100%;
            padding: 40px;
            position: relative;
            z-index: 10;
        }
        
        .test-logo {
            position: fixed;
            top: 40px;
            left: 40px;
            color: white;
            font-size: 32px;
            font-weight: 700;
            letter-spacing: -0.5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            color: var(--workeem-text);
            font-size: 28px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 32px;
            margin-top: 0;
        }
        
        .test-form-group {
            margin-bottom: 24px;
        }
        
        .test-label {
            color: var(--workeem-text);
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 8px;
            display: block;
        }
        
        .test-input {
            height: 56px;
            padding: 0 20px;
            border: 2px solid #e5e7eb;
            border-radius: 16px;
            font-size: 16px;
            background: white;
            transition: all 0.3s ease;
            box-sizing: border-box;
            width: 100%;
            font-family: inherit;
        }
        
        .test-input:focus {
            outline: none;
            border-color: var(--workeem-primary);
            background: white;
            box-shadow: 0 0 0 4px rgba(124, 58, 237, 0.1);
            transform: translateY(-1px);
        }
        
        .test-input::placeholder {
            color: #9ca3af;
            font-size: 15px;
        }
        
        .test-button {
            background: var(--workeem-primary);
            border: none;
            border-radius: 16px;
            height: 56px;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            color: white;
            cursor: pointer;
            font-family: inherit;
            letter-spacing: 0.5px;
        }
        
        .test-button:hover {
            background: var(--workeem-primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
        }
        
        .test-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            font-size: 14px;
        }
        
        .test-checkbox {
            display: flex;
            align-items: center;
            color: var(--workeem-text);
        }
        
        .test-checkbox input {
            margin-right: 8px;
            transform: scale(1.2);
        }
        
        .test-link {
            color: var(--workeem-primary);
            text-decoration: none;
            font-weight: 500;
        }
        
        .test-link:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .test-logo {
                top: 20px;
                left: 20px;
                font-size: 28px;
            }
            
            .test-card {
                margin: 20px;
                padding: 32px 24px;
                border-radius: 20px;
            }
            
            .test-title {
                font-size: 24px;
                margin-bottom: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-logo">WORKEEM</div>
        
        <div class="test-card">
            <h1 class="test-title">Connexion à votre espace</h1>
            
            <form>
                <div class="test-form-group">
                    <label class="test-label" for="username">Nom d'utilisateur ou email</label>
                    <input class="test-input" type="text" id="username" name="username" placeholder="Nom d'utilisateur ou email" autofocus>
                </div>
                
                <div class="test-form-group">
                    <label class="test-label" for="password">Mot de passe</label>
                    <input class="test-input" type="password" id="password" name="password" placeholder="Mot de passe">
                </div>
                
                <div class="test-options">
                    <label class="test-checkbox">
                        <input type="checkbox" name="rememberMe">
                        Se souvenir de moi
                    </label>
                    <a href="#" class="test-link">Mot de passe oublié ?</a>
                </div>
                
                <button type="submit" class="test-button">Se connecter</button>
            </form>
        </div>
    </div>
    
    <script>
        // Simulation d'animation d'entrée
        document.addEventListener('DOMContentLoaded', function() {
            const card = document.querySelector('.test-card');
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
