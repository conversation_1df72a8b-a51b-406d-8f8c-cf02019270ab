/* Thème Keycloak Account Management v3 Workeem - Identique à tenant-selection */

/* Variables CSS pour les couleurs Workeem EXACTES */
:root {
  --workeem-primary: #7c3aed !important;
  --workeem-primary-hover: #6d28d9 !important;
  --workeem-primary-active: #5b21b6 !important;
  --workeem-primary-soft: #ede9fe !important;
  --workeem-text: #333 !important;
  --workeem-text-light: #666 !important;
  --workeem-text-muted: #6b7280 !important;
  --workeem-border: #e1e5e9 !important;
  --workeem-background: linear-gradient(135deg, #e8e5ff 0%, #d4c5ff 50%, #c8b5ff 100%) !important;
  --workeem-card-bg: rgba(255, 255, 255, 0.95) !important;
  --workeem-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
}

/* Reset et base */
* {
  box-sizing: border-box !important;
}

/* Container principal - EXACTEMENT comme tenant-selection */
html, body {
  margin: 0 !important;
  padding: 0 !important;
  height: 100% !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  background: var(--workeem-background) !important;
}

/* Override des couleurs primaires Keycloak */
.pf-c-button.pf-m-primary,
.pf-c-button--pf-m-primary {
  background-color: var(--workeem-primary) !important;
  border-color: var(--workeem-primary) !important;
  color: white !important;
}

.pf-c-button.pf-m-primary:hover,
.pf-c-button--pf-m-primary:hover {
  background-color: var(--workeem-primary-hover) !important;
  border-color: var(--workeem-primary-hover) !important;
}

.pf-c-button.pf-m-primary:active,
.pf-c-button--pf-m-primary:active {
  background-color: var(--workeem-primary-active) !important;
  border-color: var(--workeem-primary-active) !important;
}

/* Links et textes primaires */
.pf-c-nav__link.pf-m-current,
.pf-c-nav__link:hover,
a:hover,
.pf-c-title {
  color: var(--workeem-primary) !important;
}

/* Background principal */
.pf-c-page,
.pf-c-page__main,
.pf-c-page__main-section {
  background: var(--workeem-background) !important;
}

/* Header/Navigation */
.pf-c-page__header {
  background: var(--workeem-primary) !important;
  border-bottom: none !important;
  box-shadow: var(--workeem-shadow) !important;
}

.pf-c-page__header-brand-link,
.pf-c-page__header-brand-link:hover {
  color: white !important;
}

.pf-c-page__header-tools-item .pf-c-button {
  color: rgba(255, 255, 255, 0.9) !important;
}

.pf-c-page__header-tools-item .pf-c-button:hover {
  color: white !important;
  background: rgba(255, 255, 255, 0.1) !important;
}

/* Sidebar navigation */
.pf-c-nav {
  background: var(--workeem-card-bg) !important;
  backdrop-filter: blur(20px) !important;
  border-radius: 20px !important;
  box-shadow: var(--workeem-shadow) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  margin: 20px !important;
}

.pf-c-nav__item .pf-c-nav__link {
  color: var(--workeem-text) !important;
  border-radius: 12px !important;
  margin: 4px 12px !important;
  padding: 12px 16px !important;
  transition: all 0.2s ease !important;
}

.pf-c-nav__item .pf-c-nav__link:hover {
  background: var(--workeem-primary-soft) !important;
  color: var(--workeem-primary) !important;
}

.pf-c-nav__item .pf-c-nav__link.pf-m-current {
  background: var(--workeem-primary) !important;
  color: white !important;
}

/* Content area */
.pf-c-card,
.pf-c-panel {
  background: var(--workeem-card-bg) !important;
  backdrop-filter: blur(20px) !important;
  border-radius: 20px !important;
  box-shadow: var(--workeem-shadow) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  margin-bottom: 20px !important;
}

.pf-c-card__header,
.pf-c-panel__header {
  background: transparent !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
  border-radius: 20px 20px 0 0 !important;
  padding: 20px 30px !important;
}

.pf-c-card__title,
.pf-c-panel__title {
  color: var(--workeem-text) !important;
  font-weight: 600 !important;
  font-size: 18px !important;
}

.pf-c-card__body,
.pf-c-panel__main {
  padding: 30px !important;
}

/* Forms */
.pf-c-form__group {
  margin-bottom: 24px !important;
}

.pf-c-form__label {
  color: var(--workeem-text) !important;
  font-weight: 600 !important;
  font-size: 14px !important;
}

.pf-c-form-control {
  height: 50px !important;
  padding: 0 16px !important;
  border: 1px solid var(--workeem-border) !important;
  border-radius: 12px !important;
  font-size: 16px !important;
  background: #f8f9fa !important;
  transition: all 0.2s ease !important;
}

.pf-c-form-control:focus {
  outline: none !important;
  border-color: var(--workeem-primary) !important;
  background: white !important;
  box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1) !important;
}

/* Tables */
.pf-c-table {
  background: white !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
}

.pf-c-table th {
  background: var(--workeem-primary-soft) !important;
  color: var(--workeem-text) !important;
  font-weight: 600 !important;
  border: none !important;
  padding: 16px !important;
}

.pf-c-table td {
  border: none !important;
  padding: 16px !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

/* Alerts */
.pf-c-alert.pf-m-success {
  background: #d1fae5 !important;
  border: 1px solid #a7f3d0 !important;
  color: #065f46 !important;
  border-radius: 12px !important;
}

.pf-c-alert.pf-m-danger {
  background: #fee2e2 !important;
  border: 1px solid #fecaca !important;
  color: #991b1b !important;
  border-radius: 12px !important;
}

.pf-c-alert.pf-m-info {
  background: #dbeafe !important;
  border: 1px solid #bfdbfe !important;
  color: #1e40af !important;
  border-radius: 12px !important;
}

.pf-c-alert.pf-m-warning {
  background: #fef3c7 !important;
  border: 1px solid #fde68a !important;
  color: #92400e !important;
  border-radius: 12px !important;
}

/* Tabs */
.pf-c-tabs__list {
  border-bottom: 2px solid var(--workeem-primary-soft) !important;
}

.pf-c-tabs__link {
  color: var(--workeem-text-light) !important;
  border: none !important;
  border-radius: 12px 12px 0 0 !important;
  padding: 12px 20px !important;
  margin-right: 4px !important;
}

.pf-c-tabs__item.pf-m-current .pf-c-tabs__link {
  background: var(--workeem-primary) !important;
  color: white !important;
  border: none !important;
}

.pf-c-tabs__link:hover {
  background: var(--workeem-primary-soft) !important;
  color: var(--workeem-primary) !important;
  border: none !important;
}

/* Modals */
.pf-c-modal-box {
  background: var(--workeem-card-bg) !important;
  backdrop-filter: blur(20px) !important;
  border-radius: 20px !important;
  box-shadow: var(--workeem-shadow) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.pf-c-modal-box__header {
  background: transparent !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
  border-radius: 20px 20px 0 0 !important;
}

.pf-c-modal-box__title {
  color: var(--workeem-text) !important;
  font-weight: 600 !important;
}

/* Breadcrumbs */
.pf-c-breadcrumb__link {
  color: var(--workeem-text-light) !important;
}

.pf-c-breadcrumb__link:hover {
  color: var(--workeem-primary) !important;
}

/* Dropdowns */
.pf-c-dropdown__menu {
  background: var(--workeem-card-bg) !important;
  backdrop-filter: blur(20px) !important;
  border-radius: 12px !important;
  box-shadow: var(--workeem-shadow) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.pf-c-dropdown__menu-item {
  color: var(--workeem-text) !important;
  padding: 8px 16px !important;
}

.pf-c-dropdown__menu-item:hover {
  background: var(--workeem-primary-soft) !important;
  color: var(--workeem-primary) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pf-c-nav {
    margin: 10px !important;
    padding: 10px !important;
  }
  
  .pf-c-card, .pf-c-panel {
    margin: 10px !important;
  }
  
  .pf-c-card__header, .pf-c-card__body, .pf-c-panel__header, .pf-c-panel__main {
    padding: 20px !important;
  }
}
