<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Titres Connexion - Thème Workeem</title>
    <link rel="stylesheet" href="workeem/login/resources/css/workeem.css">
    <style>
        .demo-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .demo-card {
            position: relative;
        }
        
        .demo-label {
            position: absolute;
            top: -30px;
            left: 0;
            background: var(--workeem-primary);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .comparison-title {
            text-align: center;
            color: var(--workeem-primary);
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 40px;
            margin-top: 20px;
        }
        
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .before, .after {
            text-align: center;
            padding: 20px;
            border-radius: 12px;
        }
        
        .before {
            background: #fee2e2;
            border: 2px solid #fecaca;
        }
        
        .after {
            background: #d1fae5;
            border: 2px solid #a7f3d0;
        }
        
        .before h3 {
            color: #991b1b;
            margin-top: 0;
        }
        
        .after h3 {
            color: #065f46;
            margin-top: 0;
        }
        
        .title-demo {
            font-size: 18px;
            color: #666;
            margin: 10px 0;
            font-style: italic;
        }
        
        .title-demo.small {
            font-size: 14px;
            color: #999;
        }
        
        .title-demo.large {
            font-size: 24px;
            color: #333;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .demo-container {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .before-after {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="tenant-selection-container">
        <div class="logo-section">
            <img src="workeem/login/resources/img/logo.png" alt="Workeem" class="logo" onerror="this.style.display='none'">
        </div>
        
        <div class="comparison-title">Correction des Titres de Connexion</div>
        
        <div class="before-after">
            <div class="before">
                <h3>❌ AVANT</h3>
                <div class="title-demo small">Connexion à votre compte</div>
                <p>Titre trop petit (18px)</p>
                <div class="title-demo">Bienvenue sur Workeem</div>
                <p>Titre incohérent pour la page mot de passe</p>
            </div>
            
            <div class="after">
                <h3>✅ APRÈS</h3>
                <div class="title-demo large">Connexion à votre compte</div>
                <p>Titre bien visible (24px)</p>
                <div class="title-demo large">Saisissez votre mot de passe</div>
                <p>Titre cohérent pour chaque étape</p>
            </div>
        </div>
        
        <div class="demo-container">
            <div class="demo-card">
                <div class="demo-label">Étape 1 : Nom d'utilisateur</div>
                <div class="login-card">
                    <div class="card-header">
                        <h1>Connexion à votre compte</h1>
                    </div>
                    
                    <form class="login-form">
                        <div class="form-group">
                            <label class="form-label">Nom d'utilisateur ou email</label>
                            <div class="input-wrapper">
                                <input type="text" class="form-input" placeholder="Nom d'utilisateur ou email" value="<EMAIL>">
                            </div>
                        </div>
                        
                        <button type="button" class="submit-button">
                            Continuer
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="demo-card">
                <div class="demo-label">Étape 2 : Mot de passe</div>
                <div class="login-card">
                    <div class="card-header">
                        <h1>Saisissez votre mot de passe</h1>
                    </div>
                    
                    <form class="login-form">
                        <div class="form-group">
                            <label class="form-label">Mot de passe</label>
                            <div class="input-wrapper">
                                <input type="password" class="form-input" placeholder="Saisissez votre mot de passe">
                            </div>
                        </div>
                        
                        <div class="checkbox">
                            <label>
                                <input type="checkbox"> Se souvenir de moi
                            </label>
                        </div>
                        
                        <button type="button" class="submit-button">
                            Se connecter
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="login-card" style="max-width: 600px; margin: 40px auto;">
            <div class="card-header">
                <h2 style="color: var(--workeem-primary); margin-bottom: 20px;">Corrections Apportées</h2>
            </div>
            
            <div style="text-align: left; padding: 0 20px;">
                <h3 style="color: var(--workeem-text); font-size: 18px;">✅ Problèmes Résolus :</h3>
                <ul style="color: var(--workeem-text-light); line-height: 1.6;">
                    <li><strong>Titre trop petit</strong> : Augmenté de 18px à 24px</li>
                    <li><strong>Titre incohérent</strong> : "Bienvenue sur Workeem" → "Saisissez votre mot de passe"</li>
                    <li><strong>Templates spécifiques</strong> : Création de login-username.ftl et login-password.ftl</li>
                    <li><strong>Messages personnalisés</strong> : Ajout dans messages_fr.properties</li>
                    <li><strong>Phrase hors sujet</strong> : Suppression de "Pour accéder à votre espace de gestion"</li>
                </ul>
                
                <h3 style="color: var(--workeem-text); font-size: 18px; margin-top: 30px;">📋 Fichiers Modifiés :</h3>
                <ul style="color: var(--workeem-text-muted); font-family: monospace; font-size: 13px;">
                    <li>keycloak-theme/workeem/login/login.ftl</li>
                    <li>keycloak-theme/workeem/login/login-username.ftl (nouveau)</li>
                    <li>keycloak-theme/workeem/login/login-password.ftl (nouveau)</li>
                    <li>keycloak-theme/workeem/login/template.ftl</li>
                    <li>keycloak-theme/workeem/login/messages/messages_fr.properties</li>
                    <li>keycloak-theme/workeem/login/resources/css/workeem.css</li>
                </ul>
            </div>
            
            <div class="card-footer">
                <p>© 2024 Workeem. Thème Keycloak personnalisé.</p>
            </div>
        </div>
    </div>
</body>
</html>
