<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Complet - Thèmes Workeem Keycloak</title>
    <style>
        /* Variables CSS pour les couleurs Workeem EXACTES */
        :root {
            --workeem-primary: #7c3aed;
            --workeem-primary-hover: #6d28d9;
            --workeem-primary-active: #5b21b6;
            --workeem-primary-soft: #ede9fe;
            --workeem-text: #333;
            --workeem-text-light: #666;
            --workeem-text-muted: #6b7280;
            --workeem-border: #e1e5e9;
            --workeem-background: linear-gradient(135deg, #e8e5ff 0%, #d4c5ff 50%, #c8b5ff 100%);
            --workeem-card-bg: rgba(255, 255, 255, 0.95);
            --workeem-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--workeem-background);
            color: var(--workeem-text);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 60px;
        }
        
        .logo {
            font-size: 48px;
            font-weight: 700;
            color: var(--workeem-primary);
            margin-bottom: 20px;
            letter-spacing: -1px;
        }
        
        .subtitle {
            font-size: 18px;
            color: var(--workeem-text-light);
            margin-bottom: 40px;
        }
        
        .themes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }
        
        .theme-card {
            background: var(--workeem-card-bg);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: var(--workeem-shadow);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 30px;
            text-align: center;
            transition: transform 0.2s ease;
        }
        
        .theme-card:hover {
            transform: translateY(-5px);
        }
        
        .theme-icon {
            width: 80px;
            height: 80px;
            background: var(--workeem-primary-soft);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 32px;
            color: var(--workeem-primary);
        }
        
        .theme-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--workeem-text);
            margin-bottom: 10px;
        }
        
        .theme-description {
            font-size: 14px;
            color: var(--workeem-text-light);
            line-height: 1.5;
            margin-bottom: 20px;
        }
        
        .theme-features {
            list-style: none;
            padding: 0;
            margin: 0;
            text-align: left;
        }
        
        .theme-features li {
            font-size: 13px;
            color: var(--workeem-text-muted);
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .theme-features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: var(--workeem-primary);
            font-weight: bold;
        }
        
        .color-palette {
            background: var(--workeem-card-bg);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: var(--workeem-shadow);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 30px;
            margin-bottom: 40px;
        }
        
        .palette-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--workeem-text);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .colors-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
        }
        
        .color-item {
            text-align: center;
        }
        
        .color-swatch {
            width: 100%;
            height: 60px;
            border-radius: 12px;
            margin-bottom: 10px;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .color-name {
            font-size: 12px;
            font-weight: 600;
            color: var(--workeem-text);
            margin-bottom: 4px;
        }
        
        .color-value {
            font-size: 11px;
            color: var(--workeem-text-muted);
            font-family: monospace;
        }
        
        .deployment-info {
            background: var(--workeem-card-bg);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: var(--workeem-shadow);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 30px;
            text-align: center;
        }
        
        .deployment-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--workeem-text);
            margin-bottom: 15px;
        }
        
        .deployment-text {
            font-size: 14px;
            color: var(--workeem-text-light);
            line-height: 1.6;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid var(--workeem-border);
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            font-size: 13px;
            color: var(--workeem-text);
            margin: 15px 0;
            text-align: left;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
            }
            
            .logo {
                font-size: 36px;
            }
            
            .themes-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .colors-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">WORKEEM</div>
            <div class="subtitle">Thèmes Keycloak - Identité Visuelle Complète</div>
        </div>
        
        <div class="themes-grid">
            <div class="theme-card">
                <div class="theme-icon">🔐</div>
                <div class="theme-title">Login Theme</div>
                <div class="theme-description">Interface de connexion avec le même design que tenant-selection</div>
                <ul class="theme-features">
                    <li>Background gradient identique</li>
                    <li>Logo Workeem intégré</li>
                    <li>Couleurs primaires cohérentes</li>
                    <li>Design responsive</li>
                </ul>
            </div>
            
            <div class="theme-card">
                <div class="theme-icon">👤</div>
                <div class="theme-title">Account Theme</div>
                <div class="theme-description">Gestion de profil utilisateur avec l'identité Workeem</div>
                <ul class="theme-features">
                    <li>Interface de profil personnalisée</li>
                    <li>Formulaires stylisés</li>
                    <li>Navigation cohérente</li>
                    <li>Cards avec backdrop-filter</li>
                </ul>
            </div>
            
            <div class="theme-card">
                <div class="theme-icon">⚙️</div>
                <div class="theme-title">Admin Theme</div>
                <div class="theme-description">Console d'administration avec le style Workeem</div>
                <ul class="theme-features">
                    <li>Interface admin personnalisée</li>
                    <li>Tableaux et formulaires stylisés</li>
                    <li>Navigation administrative</li>
                    <li>Modals et alertes cohérentes</li>
                </ul>
            </div>
            
            <div class="theme-card">
                <div class="theme-icon">📧</div>
                <div class="theme-title">Email Theme</div>
                <div class="theme-description">Templates d'emails avec l'identité Workeem</div>
                <ul class="theme-features">
                    <li>Templates HTML personnalisés</li>
                    <li>Header avec logo Workeem</li>
                    <li>Boutons et liens stylisés</li>
                    <li>Footer avec informations</li>
                </ul>
            </div>
        </div>
        
        <div class="color-palette">
            <div class="palette-title">Palette de Couleurs Workeem</div>
            <div class="colors-grid">
                <div class="color-item">
                    <div class="color-swatch" style="background: #7c3aed;"></div>
                    <div class="color-name">Primary</div>
                    <div class="color-value">#7c3aed</div>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: #6d28d9;"></div>
                    <div class="color-name">Primary Hover</div>
                    <div class="color-value">#6d28d9</div>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: #5b21b6;"></div>
                    <div class="color-name">Primary Active</div>
                    <div class="color-value">#5b21b6</div>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: #ede9fe;"></div>
                    <div class="color-name">Primary Soft</div>
                    <div class="color-value">#ede9fe</div>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: linear-gradient(135deg, #e8e5ff 0%, #d4c5ff 50%, #c8b5ff 100%);"></div>
                    <div class="color-name">Background</div>
                    <div class="color-value">Gradient</div>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(20px);"></div>
                    <div class="color-name">Card Background</div>
                    <div class="color-value">rgba(255,255,255,0.95)</div>
                </div>
            </div>
        </div>
        
        <div class="deployment-info">
            <div class="deployment-title">Déploiement</div>
            <div class="deployment-text">
                Pour déployer tous les thèmes, utilisez le script de déploiement :
                <div class="code-block">./deploy-theme.sh /opt/keycloak</div>
                
                Puis configurez dans Keycloak Admin Console > Realm Settings > Themes :
                <div class="code-block">
Login Theme: workeem<br>
Account Theme: workeem<br>
Admin Console Theme: workeem<br>
Email Theme: workeem
                </div>
            </div>
        </div>
    </div>
</body>
</html>
