{"name": "workeem", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production", "build:netlify": "ng build --configuration production --base-href=/", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.1.0", "@angular/common": "^19.1.0", "@angular/compiler": "^19.1.0", "@angular/core": "^19.1.0", "@angular/forms": "^19.1.0", "@angular/platform-browser": "^19.1.0", "@angular/platform-browser-dynamic": "^19.1.0", "@angular/router": "^19.1.0", "@canvasjs/angular-charts": "^1.2.0", "@canvasjs/charts": "^3.13.3", "@fullcalendar/angular": "^6.1.18", "@fullcalendar/core": "^6.1.18", "@fullcalendar/daygrid": "^6.1.18", "@fullcalendar/interaction": "^6.1.18", "@fullcalendar/list": "^6.1.18", "@fullcalendar/timegrid": "^6.1.18", "@netlify/angular-runtime": "^2.4.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "keycloak-js": "^26.2.0", "ng-zorro-antd": "^19.3.1", "ngx-permissions": "^19.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.5", "@angular/cli": "^19.1.5", "@angular/compiler-cli": "^19.1.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "tailwindcss": "^3.4.17", "typescript": "~5.7.2"}}