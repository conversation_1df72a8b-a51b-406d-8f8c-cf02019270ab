import { Component, Input, Output, EventEmitter, OnInit, OnChanges, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzListModule } from 'ng-zorro-antd/list';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';

import { Member, MemberType, MemberStatus, MemberHistory } from '../../models/member.model';
import { MemberService } from '../../services/member.service';

@Component({
  selector: 'app-member-details',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzTagModule,
    NzButtonModule,
    NzIconModule,
    NzDividerModule,
    NzListModule,
    NzSpinModule,
    NzEmptyModule,
    NzToolTipModule
  ],
  templateUrl: './member-details.component.html',
  styleUrl: './member-details.component.css'
})
export class MemberDetailsComponent implements OnInit, OnChanges {
  @Input() member: Member | null = null;
  @Input() visible: boolean = false;
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() editMember = new EventEmitter<Member>();

  // Signaux pour l'historique
  private historySignal = signal<MemberHistory[]>([]);
  private loadingHistorySignal = signal<boolean>(false);

  // Getters pour les templates
  get history() { return this.historySignal(); }
  get loadingHistory() { return this.loadingHistorySignal(); }

  // Enums pour les templates
  MemberType = MemberType;
  MemberStatus = MemberStatus;

  constructor(private memberService: MemberService) {}

  ngOnInit() {
    if (this.member) {
      this.loadMemberHistory();
    }
  }

  ngOnChanges() {
    if (this.member && this.visible) {
      this.loadMemberHistory();
    }
  }

  loadMemberHistory() {
    if (!this.member) return;

    this.loadingHistorySignal.set(true);
    this.memberService.getMemberHistory(this.member.id).subscribe({
      next: (history) => {
        this.historySignal.set(history);
        this.loadingHistorySignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement de l\'historique:', error);
        this.loadingHistorySignal.set(false);
      }
    });
  }

  close() {
    this.visibleChange.emit(false);
  }

  onEditMember() {
    if (this.member) {
      this.editMember.emit(this.member);
    }
  }

  getStatusColor(status: MemberStatus): string {
    switch (status) {
      case MemberStatus.ACTIVE: return 'green';
      case MemberStatus.INACTIVE: return 'orange';
      case MemberStatus.SUSPENDED: return 'red';
      default: return 'default';
    }
  }

  getStatusText(status: MemberStatus): string {
    switch (status) {
      case MemberStatus.ACTIVE: return 'Actif';
      case MemberStatus.INACTIVE: return 'Inactif';
      case MemberStatus.SUSPENDED: return 'Suspendu';
      default: return status;
    }
  }

  getTypeText(type: MemberType): string {
    return type === MemberType.STUDENT ? 'Étudiant' : 'Professionnel';
  }

  getTypeColor(type: MemberType): string {
    return type === MemberType.STUDENT ? 'blue' : 'purple';
  }

  getHistoryIcon(type: string): string {
    switch (type) {
      case 'payment': return 'dollar';
      case 'reservation': return 'calendar';
      case 'subscription_change': return 'credit-card';
      case 'status_change': return 'user';
      default: return 'file-text';
    }
  }

  getHistoryColor(type: string): string {
    switch (type) {
      case 'payment': return '#52c41a';
      case 'reservation': return '#1890ff';
      case 'subscription_change': return '#722ed1';
      case 'status_change': return '#fa8c16';
      default: return '#8c8c8c';
    }
  }

  formatAmount(amount?: number): string {
    if (!amount) return '';
    return `${amount} MAD`;
  }
}
