.member-details-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.member-details-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* En-tête */
.details-header {
  padding: 24px;
  border-bottom: 1px solid #E5E5EA;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: #F2F2F7;
}

.member-info {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.member-avatar {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  background-color: #6E56CF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 24px;
  flex-shrink: 0;
}

.member-basic-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.member-name {
  font-size: 24px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0;
}

.member-id {
  font-size: 14px;
  color: #8E8E93;
  margin: 0;
}

.member-badges {
  display: flex;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.header-actions button {
  border-radius: 8px !important;
}

/* Contenu */
.details-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-card {
  border-radius: 12px !important;
  border: 1px solid #E5E5EA !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-size: 12px;
  font-weight: 500;
  color: #8E8E93;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item span {
  font-size: 14px;
  color: #1C1C1E;
  font-weight: 500;
}

.student-code {
  font-family: 'Courier New', monospace !important;
  background-color: #EDE9F8 !important;
  color: #6E56CF !important;
  padding: 4px 8px !important;
  border-radius: 6px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  display: inline-block !important;
}

.ice-number {
  font-family: 'Courier New', monospace !important;
  background-color: #F3E8FF !important;
  color: #8B5CF6 !important;
  padding: 4px 8px !important;
  border-radius: 6px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  display: inline-block !important;
}

/* Abonnement */
.subscription-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.subscription-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #1C1C1E;
}

.subscription-name .anticon {
  color: #6E56CF;
  font-size: 18px;
}

/* Historique */
.history-card {
  flex: 1;
}

.history-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

.history-amount {
  font-weight: 600;
  color: #52c41a;
  margin-left: 8px;
}

.ant-list-item-meta-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ant-list-item-meta-description {
  color: #8E8E93 !important;
  font-size: 12px !important;
}

/* Responsive */
@media (max-width: 768px) {
  .member-details-overlay {
    padding: 12px;
  }

  .member-details-panel {
    max-height: 95vh;
  }

  .details-header {
    padding: 16px;
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .member-info {
    gap: 12px;
  }

  .member-avatar {
    width: 48px;
    height: 48px;
    font-size: 18px;
  }

  .member-name {
    font-size: 20px;
  }

  .header-actions {
    justify-content: flex-end;
  }

  .details-content {
    padding: 16px;
    gap: 16px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

/* Animations */
.member-details-overlay {
  animation: fadeIn 0.2s ease-out;
}

.member-details-panel {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Scrollbar personnalisée */
.details-content::-webkit-scrollbar {
  width: 6px;
}

.details-content::-webkit-scrollbar-track {
  background: #F2F2F7;
  border-radius: 3px;
}

.details-content::-webkit-scrollbar-thumb {
  background: #C7C7CC;
  border-radius: 3px;
}

.details-content::-webkit-scrollbar-thumb:hover {
  background: #AEAEB2;
}
