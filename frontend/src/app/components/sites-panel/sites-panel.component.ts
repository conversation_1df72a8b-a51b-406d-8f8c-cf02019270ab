import { Component, OnInit, OnDestroy, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
// import { Subject, takeUntil } from 'rxjs'; // Plus nécessaire

import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzMenuModule } from 'ng-zorro-antd/menu';

import { SiteService } from '../../services/site.service';
import { AuthService } from '../../services/auth.service';
import { SiteContextService } from '../../services/site-context.service';
import { Site } from '../../models/site.model';
import { ErrorService, ValidationMessages } from '../../services/error.service';
import { slideDownAnimation, fadeInAnimation } from '../../animations/slide.animation';

@Component({
  selector: 'app-sites-panel',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzButtonModule,
    NzIconModule,
    NzDropDownModule,
    NzModalModule,
    NzFormModule,
    NzInputModule,
    NzSwitchModule,
    NzGridModule,
    NzMenuModule
  ],
  templateUrl: './sites-panel.component.html',
  styleUrls: ['./sites-panel.component.css'],
  animations: [slideDownAnimation, fadeInAnimation]
})
export class SitesPanelComponent implements OnInit {

  @Input() sidebarCollapsed = false;

  isExpanded = false;
  sites: Site[] = [];
  currentSite: Site | null = null;

  // Modal
  isModalVisible = false;
  modalTitle = '';
  editingSite: Site | null = null;
  saving = false;

  siteForm: FormGroup;

  validation_messages: ValidationMessages = {
    'name': [
      { type: 'required', message: 'Le nom du site est obligatoire' },
      { type: 'maxlength', message: 'Le nom ne peut pas dépasser 100 caractères' }
    ],
    'city': [
      { type: 'maxlength', message: 'La ville ne peut pas dépasser 50 caractères' }
    ],
    'country': [
      { type: 'maxlength', message: 'Le pays ne peut pas dépasser 50 caractères' }
    ],
    'address': [
      { type: 'maxlength', message: 'L\'adresse ne peut pas dépasser 255 caractères' }
    ]
  };

  constructor(
    private siteService: SiteService,
    private authService: AuthService,
    private siteContext: SiteContextService,
    private fb: FormBuilder,
    private message: NzMessageService,
    private router: Router,
    private modal: NzModalService,
    private errorService: ErrorService
  ) {
    this.siteForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      city: ['', [Validators.maxLength(50)]],
      country: ['', [Validators.maxLength(50)]],
      address: ['', [Validators.maxLength(255)]],
      isActive: [true]
    });
  }

  async ngOnInit() {
    // Ne charger les sites que si l'utilisateur est authentifié
    if (this.authService.isAuthenticated()) {
      await this.loadSites();
      // Charger le site actuel depuis le contexte
      await this.loadCurrentSite();
    } else {
      console.log('🔒 User not authenticated, skipping sites loading');
    }
  }

  async loadSites() {
    try {
      this.sites = await this.siteService.getSites();
    } catch (error) {
      console.error('Erreur lors du chargement des sites:', error);
      this.sites = [];
    }
  }

  async loadCurrentSite() {
    try {
      const currentSite = await this.siteContext.loadCurrentSite();
      if (currentSite) {
        this.currentSite = currentSite;
      } else if (this.sites.length > 0) {
        // Si aucun site n'est sélectionné, prendre le premier actif
        const defaultSite = this.sites.find(site => site.isActive) || this.sites[0];
        if (defaultSite) {
          await this.selectSite(defaultSite);
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement du site actuel:', error);
    }
  }



  togglePanel() {
    this.isExpanded = !this.isExpanded;
  }

  closePanel() {
    this.isExpanded = false;
  }

  async selectSite(site: Site) {
    if (!site.isActive) {
      this.message.warning('Ce site est désactivé');
      return;
    }

    try {
      // Sélectionner le site via le contexte (met à jour backend + contexte)
      const selectedSite = await this.siteContext.selectSite(site.id);
      if (selectedSite) {
        this.currentSite = selectedSite;
        this.closePanel();
        this.message.success(`Basculé vers ${site.name}`);

        // Redirection intelligente basée sur la page actuelle
        this.handleIntelligentRedirection();
      }
    } catch (error) {
      console.error('Erreur lors de la sélection du site:', error);
      this.message.error('Erreur lors de la sélection du site');
    }
  }

  private handleIntelligentRedirection() {
    const currentUrl = this.router.url;
    console.log('🔄 Current URL:', currentUrl);

    // Définir les routes principales et leurs sous-routes
    const routeMapping: { [key: string]: string } = {
      '/welcome': '/welcome',
      '/reservations': '/reservations',
      '/spaces': '/spaces',
      '/members': '/members',
      '/billing': '/billing',
      '/subscriptions': '/subscriptions',
      '/statistics': '/statistics'
    };

    // Trouver la route principale correspondante
    let targetRoute = '/welcome'; // Route par défaut

    for (const [mainRoute, redirectTo] of Object.entries(routeMapping)) {
      if (currentUrl.startsWith(mainRoute)) {
        // Si on est sur une page de détail (ex: /reservations/1), rediriger vers la page principale
        if (currentUrl !== mainRoute && currentUrl.includes('/') && currentUrl.split('/').length > 2) {
          targetRoute = redirectTo;
        } else {
          // Si on est déjà sur la page principale, rester dessus
          targetRoute = currentUrl;
        }
        break;
      }
    }

    console.log('🎯 Redirecting to:', targetRoute);

    // Effectuer la redirection
    if (targetRoute !== currentUrl) {
      this.router.navigate([targetRoute]);
    }
    // Si on reste sur la même page, le rechargement des données se fera via l'observable du site context
  }

  openAddSiteModal() {
    // Vérifier si l'utilisateur a les permissions pour créer des sites (multi-site)
    if (!this.authService.canAccessMultiSite()) {
      this.showUpgradeModal();
      return;
    }

    this.modalTitle = 'Ajouter un nouveau site';
    this.editingSite = null;
    this.siteForm.reset({
      name: '',
      city: '',
      country: '',
      address: '',
      isActive: true
    });
    this.isModalVisible = true;
  }

  editSite(site: Site) {
    this.modalTitle = 'Modifier le site';
    this.editingSite = site;
    this.siteForm.patchValue({
      name: site.name,
      city: site.city,
      country: site.country,
      address: site.address,
      isActive: site.isActive
    });
    this.isModalVisible = true;
  }

  async saveSite() {
    if (this.siteForm.valid) {
      this.saving = true;
      const formValue = this.siteForm.value;

      // S'assurer que tous les champs requis sont présents
      const siteData = {
        name: formValue.name || '',
        city: formValue.city || '',
        country: formValue.country || '',
        address: formValue.address || '',
        isActive: formValue.isActive !== undefined ? formValue.isActive : true
      };

      console.log('Saving site with data:', siteData);

      try {
        if (this.editingSite) {
          // Modification
          await this.siteService.updateSite(this.editingSite.id, siteData);
          this.message.success('Site modifié avec succès');
        } else {
          // Ajout
          await this.siteService.addSite(siteData);
          this.message.success('Site ajouté avec succès');
        }
        await this.loadSites(); // Recharger la liste
        this.closeModal();
      } catch (error) {
        console.error('Error saving site:', error);
        this.message.error(this.editingSite ? 'Erreur lors de la modification du site' : 'Erreur lors de l\'ajout du site');
        this.saving = false;
      }
    } else {
      console.log('Form is invalid:', this.siteForm.errors);
      this.markFormGroupTouched();
      this.message.error('Veuillez remplir tous les champs requis');
    }
  }

  async toggleSiteStatus(site: Site) {
    // Vérifier si on essaie de désactiver le site actuellement sélectionné
    if (site.isActive && this.isCurrentSite(site)) {
      this.message.warning('Impossible de désactiver le site actuellement sélectionné. Veuillez d\'abord sélectionner un autre site.');
      return;
    }

    try {
      const updatedSite = await this.siteService.toggleSiteStatus(site.id);

      // Mettre à jour la liste locale
      const index = this.sites.findIndex(s => s.id === site.id);
      if (index !== -1) {
        this.sites[index] = updatedSite;
      }

      // Si c'est le site actuel, mettre à jour le contexte
      if (this.currentSite?.id === site.id) {
        this.currentSite = updatedSite;
        this.siteContext.setCurrentSite(updatedSite);
      }

      this.message.success(`Site ${updatedSite.isActive ? 'activé' : 'désactivé'} avec succès`);
    } catch (error: any) {
      console.error('Erreur lors du changement de statut:', error);

      // Gestion spécifique de l'erreur de site actuel
      if (error.error?.code === 'CURRENT_SITE_CANNOT_BE_DEACTIVATED') {
        this.message.error(error.error.message || 'Impossible de désactiver le site actuellement sélectionné');
      } else {
        this.message.error('Erreur lors de la modification du statut');
      }
    }
  }

  async deleteSite(site: Site) {
    if (this.sites.length <= 1) {
      this.message.warning('Vous ne pouvez pas supprimer le dernier site');
      return;
    }

    try {
      await this.siteService.deleteSite(site.id);
      await this.loadSites(); // Recharger la liste
      this.message.success('Site supprimé avec succès');

      // Si le site supprimé était le site actuel, basculer vers le premier site disponible
      if (this.currentSite?.id === site.id) {
        this.currentSite = this.sites.find(s => s.isActive) || this.sites[0] || null;
      }
    } catch (error) {
      this.message.error('Erreur lors de la suppression du site');
    }
  }

  closeModal() {
    this.isModalVisible = false;
    this.saving = false;
    this.editingSite = null;
  }

  private markFormGroupTouched() {
    Object.keys(this.siteForm.controls).forEach(key => {
      const control = this.siteForm.get(key);
      control?.markAsTouched();
    });
  }

  private showUpgradeModal() {
    const currentPlan = this.getCurrentPlanName();

    this.modal.warning({
      nzTitle: `🚀 Fonctionnalité Multi-site non disponible`,
      nzContent: `
        <div style="margin: 16px 0;">
          <p><strong>Votre plan actuel :</strong> ${currentPlan}</p>
          <p><strong>Cette fonctionnalité est disponible avec :</strong></p>
          <ul style="margin: 8px 0; padding-left: 20px;">
            <li>Plan PROFESSIONAL</li>
            <li>Plan ENTREPRISE</li>
          </ul>
          <p style="margin-top: 16px; color: #1890ff;">
            <strong>✨ Passez à un plan supérieur pour gérer plusieurs sites !</strong>
          </p>
        </div>
      `,
      nzOkText: 'Voir les plans',
      nzCancelText: 'Plus tard',
      nzOnOk: () => {
        // Ouvrir la page des plans dans un nouvel onglet
        window.open('https://workeem.ma/#pricing', '_blank');
      },
      nzWidth: 500,
      nzCentered: true
    });
  }

  private getCurrentPlanName(): string {
    if (this.authService.isBasicPlan()) return 'BASIC';
    if (this.authService.isStarterPlan()) return 'STARTER';
    if (this.authService.isProfessionalPlan()) return 'PROFESSIONAL';
    if (this.authService.isEnterprisePlan()) return 'ENTREPRISE';
    return 'BASIC';
  }

  /**
   * Vérifier si le site donné est le site actuellement sélectionné
   */
  isCurrentSite(site: Site): boolean {
    return this.currentSite?.id === site.id;
  }

  // Méthodes utilitaires pour la validation
  isFieldInvalid(fieldName: string): boolean {
    return this.errorService.isFieldInvalid(fieldName, this.siteForm);
  }

  getErrorMessage(controlName: string): string {
    return this.errorService.getErrorMessage(controlName, this.siteForm, this.validation_messages);
  }
}
