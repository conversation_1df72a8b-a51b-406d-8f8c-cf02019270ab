import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzListModule } from 'ng-zorro-antd/list';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { Router } from '@angular/router';

export interface Notification {
  id: string;
  type: 'reservation' | 'echeance' | 'payment' | 'system' | 'member';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
}

@Component({
  selector: 'app-notifications',
  standalone: true,
  imports: [
    CommonModule,
    NzDropDownModule,
    NzIconModule,
    NzBadgeModule,
    NzButtonModule,
    NzListModule,
    NzTagModule,
    NzEmptyModule
  ],
  template: `
    <div nz-dropdown [nzDropdownMenu]="notificationMenu" nzPlacement="bottomRight" nzTrigger="click"
         class="notification-trigger">
      <nz-badge [nzCount]="displayCount" nzSize="small" [nzOffset]="[6, -4]">
        <div class="notification-icon">
          <nz-icon nzType="bell" nzTheme="outline"></nz-icon>
        </div>
      </nz-badge>
    </div>

    <nz-dropdown-menu #notificationMenu="nzDropdownMenu">
      <div class="notification-dropdown">
        <div class="notification-header">
          <h4>Notifications</h4>
          <button nz-button nzType="link" nzSize="small" (click)="markAllAsRead()" *ngIf="unreadCount > 0">
            Tout marquer comme lu
          </button>
        </div>

        <div class="notification-content">
          <nz-list *ngIf="notifications.length > 0; else emptyTemplate" nzSize="small">
            <nz-list-item *ngFor="let notification of notifications"
                          [class.unread]="!notification.read"
                          (click)="onNotificationClick(notification)">
              <div class="notification-item">
                <div class="notification-icon">
                  <nz-icon [nzType]="getNotificationIcon(notification.type)"
                           [style.color]="getNotificationColor(notification.type)"></nz-icon>
                </div>
                <div class="notification-content-text">
                  <div class="notification-title">{{ notification.title }}</div>
                  <div class="notification-message">{{ notification.message }}</div>
                  <div class="notification-time">{{ formatTime(notification.timestamp) }}</div>
                </div>
                <div class="notification-type">
                  <nz-tag [nzColor]="getNotificationTagColor(notification.type)">
                    {{ getNotificationTypeLabel(notification.type) }}
                  </nz-tag>
                </div>
              </div>
            </nz-list-item>
          </nz-list>

          <ng-template #emptyTemplate>
            <nz-empty nzNotFoundContent="Aucune notification"></nz-empty>
          </ng-template>
        </div>

        <div class="notification-footer" *ngIf="notifications.length > 0">
          <button nz-button nzType="link" (click)="viewAllNotifications()">
            Voir toutes les notifications
          </button>
        </div>
      </div>
    </nz-dropdown-menu>
  `,
  styles: [`
    .notification-trigger {
      background: none;
      border: none;
      padding: 8px;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      width: 40px;
      height: 40px;
    }

    .notification-trigger:hover {
      background-color: rgba(110, 86, 207, 0.08);
      transform: scale(1.05);
    }

    .notification-trigger:active {
      transform: scale(0.98);
    }

    .notification-icon {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #666;
      transition: color 0.2s ease;
    }

    .notification-trigger:hover .notification-icon {
      color: #6E56CF;
    }

    .notification-icon nz-icon {
      font-size: 20px;
      line-height: 1;
    }

    /* Style Apple pour le badge */
    ::ng-deep .notification-trigger .ant-badge-count {
      background: linear-gradient(135deg, #ff4757, #ff3742) !important;
      border: 1px solid white !important;
      box-shadow: 0 3px 12px rgba(255, 71, 87, 0.4) !important;
      font-size: 10px !important;
      font-weight: 700 !important;
      min-width: 16px !important;
      height: 16px !important;
      line-height: 16px !important;
      border-radius: 8px !important;
      position: absolute !important;
      top: 3px !important;
      right: 2px !important;
      z-index: 10 !important;
      padding: 0 !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      color: white !important;
      text-align: center !important;
      overflow: hidden !important;
      white-space: nowrap !important;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
    }

    .notification-dropdown {
      width: 380px;
      max-height: 500px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .notification-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #f0f0f0;
    }

    .notification-header h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #1C1C1E;
    }

    .notification-content {
      max-height: 350px;
      overflow-y: auto;
    }

    .notification-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 12px 20px;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .notification-item:hover {
      background-color: #f8f9fa;
    }

    .unread {
      background-color: rgba(110, 86, 207, 0.02);
      border-left: 3px solid #6E56CF;
    }

    .notification-icon {
      flex-shrink: 0;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .notification-content-text {
      flex: 1;
      min-width: 0;
    }

    .notification-title {
      font-size: 14px;
      font-weight: 500;
      color: #1C1C1E;
      margin-bottom: 4px;
      line-height: 1.4;
    }

    .notification-message {
      font-size: 13px;
      color: #666;
      margin-bottom: 4px;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .notification-time {
      font-size: 12px;
      color: #999;
    }

    .notification-type {
      flex-shrink: 0;
    }

    .notification-footer {
      padding: 12px 20px;
      border-top: 1px solid #f0f0f0;
      text-align: center;
    }

    ::-webkit-scrollbar {
      width: 6px;
    }

    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    ::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  `]
})
export class NotificationsComponent implements OnInit {
  notifications: Notification[] = [];
  unreadCount = 0;

  get displayCount(): number {
    return this.unreadCount > 99 ? 99 : this.unreadCount;
  }

  constructor(private router: Router) {}

  ngOnInit() {
    this.loadNotifications();
  }

  loadNotifications() {
    // Données de test - à remplacer par un service réel
    const today = new Date();
    this.notifications = [
      {
        id: '1',
        type: 'reservation',
        title: 'Nouvelle réservation',
        message: 'Sarah Martin a réservé la Salle de réunion A pour demain à 14h30',
        timestamp: new Date(today.getTime() - 30 * 60 * 1000), // 30 min ago
        read: false,
        actionUrl: '/reservations'
      },
      {
        id: '2',
        type: 'echeance',
        title: 'Échéance de paiement',
        message: 'Le paiement de Ahmed Tazi arrive à échéance dans 3 jours',
        timestamp: new Date(today.getTime() - 2 * 60 * 60 * 1000), // 2h ago
        read: false,
        actionUrl: '/billing'
      },
      {
        id: '3',
        type: 'member',
        title: 'Nouveau membre',
        message: 'Fatima Zahra a rejoint votre espace de coworking',
        timestamp: new Date(today.getTime() - 4 * 60 * 60 * 1000), // 4h ago
        read: true,
        actionUrl: '/members'
      },
      {
        id: '4',
        type: 'payment',
        title: 'Paiement reçu',
        message: 'Paiement de 150€ reçu de Houssam Benali',
        timestamp: new Date(today.getTime() - 24 * 60 * 60 * 1000), // 1 day ago
        read: true,
        actionUrl: '/billing'
      },
      {
        id: '5',
        type: 'system',
        title: 'Maintenance programmée',
        message: 'Maintenance du système prévue ce weekend de 2h à 6h',
        timestamp: new Date(today.getTime() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        read: true
      }
    ];

    this.updateUnreadCount();
  }

  updateUnreadCount() {
    this.unreadCount = this.notifications.filter(n => !n.read).length;
  }

  getNotificationIcon(type: string): string {
    const icons = {
      reservation: 'calendar',
      echeance: 'clock-circle',
      payment: 'dollar-circle',
      system: 'setting',
      member: 'user-add'
    };
    return icons[type as keyof typeof icons] || 'bell';
  }

  getNotificationColor(type: string): string {
    const colors = {
      reservation: '#6E56CF',
      echeance: '#ff7875',
      payment: '#52c41a',
      system: '#1890ff',
      member: '#fa8c16'
    };
    return colors[type as keyof typeof colors] || '#666';
  }

  getNotificationTagColor(type: string): string {
    const colors = {
      reservation: 'purple',
      echeance: 'red',
      payment: 'green',
      system: 'blue',
      member: 'orange'
    };
    return colors[type as keyof typeof colors] || 'default';
  }

  getNotificationTypeLabel(type: string): string {
    const labels = {
      reservation: 'Réservation',
      echeance: 'Échéance',
      payment: 'Paiement',
      system: 'Système',
      member: 'Membre'
    };
    return labels[type as keyof typeof labels] || 'Notification';
  }

  formatTime(timestamp: Date): string {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) {
      return `Il y a ${minutes} min`;
    } else if (hours < 24) {
      return `Il y a ${hours}h`;
    } else {
      return `Il y a ${days} jour${days > 1 ? 's' : ''}`;
    }
  }

  onNotificationClick(notification: Notification) {
    if (!notification.read) {
      notification.read = true;
      this.updateUnreadCount();
    }

    if (notification.actionUrl) {
      this.router.navigate([notification.actionUrl]);
    }
  }

  markAllAsRead() {
    this.notifications.forEach(n => n.read = true);
    this.updateUnreadCount();
  }

  viewAllNotifications() {
    // Naviguer vers une page dédiée aux notifications
    this.router.navigate(['/notifications']);
  }
}
