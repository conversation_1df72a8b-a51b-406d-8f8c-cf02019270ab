<div class="charts-container">
  <!-- En-tête de la section -->
  <div class="charts-header">
    <h2>📊 Statistiques Financières</h2>
    <p>Analyse des revenus et factures</p>
  </div>

  <!-- Graphiques côte à côte -->
  <div class="charts-grid">

    <!-- Graphique 1: Factures par mois -->
    <div class="chart-card">
      <div class="chart-card-header">
        <h3>💰 Factures par Mois</h3>
        <div class="chart-legend">
          <span class="legend-item">
            <span class="legend-color paid"></span>
            🟢 Payées
          </span>
          <span class="legend-item">
            <span class="legend-color unpaid"></span>
            🔴 Non Payées
          </span>
        </div>
      </div>
      <div class="chart-wrapper">
        <!-- Graphique temporaire en CSS -->
        <div class="css-chart stacked-column">
          <div class="chart-bars">
            <div class="bar-group" *ngFor="let month of monthlyData">
              <div class="bar-stack">
                <div class="bar paid" [style.height.%]="month.paid"></div>
                <div class="bar unpaid" [style.height.%]="month.unpaid"></div>
              </div>
              <span class="bar-label">{{month.label}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Graphique 2: Répartition des revenus -->
    <div class="chart-card">
      <div class="chart-card-header">
        <h3>📈 Répartition des Revenus</h3>
        <div class="revenue-summary">
          <div class="revenue-item">
            <span class="revenue-color membership"></span>
            <span>Membership Fees</span>
            <strong>€156,780</strong>
          </div>
          <div class="revenue-item">
            <span class="revenue-color booking"></span>
            <span>Booking Fees</span>
            <strong>€89,450</strong>
          </div>
          <div class="revenue-item">
            <span class="revenue-color oneoff"></span>
            <span>One-off Fees</span>
            <strong>€34,560</strong>
          </div>
          <div class="revenue-item">
            <span class="revenue-color deposits"></span>
            <span>Deposits</span>
            <strong>€19,340</strong>
          </div>
        </div>
      </div>
      <div class="chart-wrapper">
        <!-- Graphique temporaire en CSS -->
        <div class="css-chart pie-chart">
          <div class="pie-container">
            <div class="pie-slice membership" [style.transform]="'rotate(0deg)'"></div>
            <div class="pie-slice booking" [style.transform]="'rotate(188deg)'"></div>
            <div class="pie-slice oneoff" [style.transform]="'rotate(295deg)'"></div>
            <div class="pie-slice deposits" [style.transform]="'rotate(336deg)'"></div>
          </div>
        </div>
      </div>
    </div>

  </div>

  <!-- Résumé financier -->
  <div class="financial-summary">
    <div class="summary-card total-revenue">
      <div class="summary-icon">💰</div>
      <div class="summary-content">
        <h4>Revenus Total</h4>
        <span class="summary-amount">€300,130</span>
        <span class="summary-period">Cette année</span>
      </div>
    </div>

    <div class="summary-card paid-invoices">
      <div class="summary-icon">✅</div>
      <div class="summary-content">
        <h4>Factures Payées</h4>
        <span class="summary-amount">€259,420</span>
        <span class="summary-growth">+12.5%</span>
      </div>
    </div>

    <div class="summary-card unpaid-invoices">
      <div class="summary-icon">⏳</div>
      <div class="summary-content">
        <h4>En Attente</h4>
        <span class="summary-amount">€42,670</span>
        <span class="summary-warning">À suivre</span>
      </div>
    </div>

    <div class="summary-card conversion-rate">
      <div class="summary-icon">📊</div>
      <div class="summary-content">
        <h4>Taux de Paiement</h4>
        <span class="summary-amount">85.8%</span>
        <span class="summary-growth">+2.3%</span>
      </div>
    </div>
  </div>
</div>
