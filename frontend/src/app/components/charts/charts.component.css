.charts-container {
  padding: 24px;
  background: #f8f9fa;
  border-radius: 12px;
  margin: 20px 0;
}

/* En-tête */
.charts-header {
  text-align: center;
  margin-bottom: 32px;
}

.charts-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 8px 0;
  font-family: 'Inter', sans-serif;
}

.charts-header p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

/* Grille des graphiques */
.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
}

/* Cartes des graphiques */
.chart-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

.chart-card-header {
  margin-bottom: 20px;
}

.chart-card-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px 0;
}

/* Légende du graphique des factures */
.chart-legend {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.paid {
  background-color: #52c41a;
}

.legend-color.unpaid {
  background-color: #ff4d4f;
}

/* Résumé des revenus */
.revenue-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.revenue-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
}

.revenue-color {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.revenue-color.membership {
  background-color: #6E56CF;
}

.revenue-color.booking {
  background-color: #B8A9E8;
}

.revenue-color.oneoff {
  background-color: #52c41a;
}

.revenue-color.deposits {
  background-color: #1890ff;
}

.revenue-item strong {
  margin-left: auto;
  color: #1a1a1a;
}

/* Wrapper des graphiques */
.chart-wrapper {
  border-radius: 8px;
  overflow: hidden;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Graphiques CSS temporaires */
.css-chart {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Graphique en colonnes empilées */
.stacked-column {
  padding: 20px;
}

.chart-bars {
  display: flex;
  align-items: end;
  justify-content: space-between;
  height: 300px;
  gap: 8px;
}

.bar-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 40px;
}

.bar-stack {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 250px;
  border-radius: 4px 4px 0 0;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bar {
  width: 100%;
  transition: all 0.3s ease;
}

.bar.paid {
  background: linear-gradient(180deg, #52c41a, #73d13d);
}

.bar.unpaid {
  background: linear-gradient(180deg, #ff4d4f, #ff7875);
}

.bar-label {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* Graphique en secteurs */
.pie-chart {
  position: relative;
}

.pie-container {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.pie-slice {
  position: absolute;
  width: 50%;
  height: 50%;
  transform-origin: 100% 100%;
}

.pie-slice.membership {
  background: #6E56CF;
  clip-path: polygon(0 0, 100% 0, 100% 100%);
  transform: rotate(0deg);
  z-index: 4;
}

.pie-slice.booking {
  background: #B8A9E8;
  clip-path: polygon(0 0, 100% 0, 100% 100%);
  transform: rotate(188deg);
  z-index: 3;
}

.pie-slice.oneoff {
  background: #52c41a;
  clip-path: polygon(0 0, 100% 0, 100% 100%);
  transform: rotate(295deg);
  z-index: 2;
}

.pie-slice.deposits {
  background: #1890ff;
  clip-path: polygon(0 0, 100% 0, 100% 100%);
  transform: rotate(336deg);
  z-index: 1;
}

/* Résumé financier */
.financial-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.summary-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.summary-icon {
  font-size: 32px;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: #f8f9fa;
}

.total-revenue .summary-icon {
  background: linear-gradient(135deg, #6E56CF, #B8A9E8);
}

.paid-invoices .summary-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.unpaid-invoices .summary-icon {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.conversion-rate .summary-icon {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.summary-content {
  flex: 1;
}

.summary-content h4 {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  margin: 0 0 4px 0;
}

.summary-amount {
  font-size: 24px;
  font-weight: 700;
  color: #1a1a1a;
  display: block;
  margin-bottom: 4px;
}

.summary-period,
.summary-growth,
.summary-warning {
  font-size: 12px;
  font-weight: 500;
}

.summary-period {
  color: #666;
}

.summary-growth {
  color: #52c41a;
}

.summary-warning {
  color: #ff4d4f;
}

/* Responsive */
@media (max-width: 768px) {
  .charts-container {
    padding: 16px;
  }

  .charts-header h2 {
    font-size: 24px;
  }

  .chart-card {
    padding: 16px;
  }

  .revenue-summary {
    grid-template-columns: 1fr;
  }

  .summary-card {
    padding: 16px;
  }

  .summary-amount {
    font-size: 20px;
  }
}
