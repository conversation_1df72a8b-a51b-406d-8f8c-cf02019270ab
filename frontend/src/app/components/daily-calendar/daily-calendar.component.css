/* Container principal */
.daily-calendar-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

/* En-tête */
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.navigation-container {
  display: flex;
  align-items: center;
  gap: 24px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.calendar-title {
  font-size: 18px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.calendar-title nz-icon {
  color: #6E56CF;
  font-size: 20px;
}

.date-navigation {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  transition: all 0.2s ease;
  color: #6E56CF;
}

.nav-button:hover {
  background-color: #f0f0f0;
  color: #5A4FCF;
}

.nav-button:active {
  transform: scale(0.95);
}

.calendar-date {
  font-size: 14px;
  color: #1C1C1E;
  margin: 0;
  text-transform: capitalize;
  font-weight: 500;
  min-width: 200px;
  text-align: center;
}

.today-button {
  display: flex;
  align-items: center;
  gap: 6px;
  border-radius: 6px;
  transition: all 0.2s ease;
  padding: 4px 12px;
  height: 32px;
  font-size: 12px;
  white-space: nowrap;
}

.today-button:not([disabled]):hover {
  border-color: #6E56CF;
  color: #6E56CF;
}

.today-button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive design */
@media (max-width: 768px) {
  .calendar-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .navigation-container {
    position: static;
    transform: none;
    left: auto;
    width: 100%;
    justify-content: center;
    flex-direction: column;
    gap: 12px;
  }

  .date-navigation {
    justify-content: center;
    width: 100%;
  }

  .calendar-date {
    min-width: 180px;
    font-size: 13px;
  }

  .today-button {
    font-size: 11px;
    padding: 4px 8px;
    align-self: center;
  }

  .header-actions {
    align-self: flex-end;
  }
}

.header-actions {
  display: flex;
  gap: 8px;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6E56CF;
  border: 1px solid rgba(110, 86, 207, 0.2);
  border-radius: 6px;
}

.refresh-btn:hover {
  background-color: rgba(110, 86, 207, 0.05);
  border-color: rgba(110, 86, 207, 0.3);
}

/* Légende en bas du calendrier */
.calendar-legend {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.legend-title {
  font-size: 14px;
  font-weight: 600;
  color: #1C1C1E;
  margin-right: 8px;
}

/* Container du calendrier */
.calendar-container {
  overflow-x: auto;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
}

/* En-tête des espaces */
.spaces-header {
  display: flex;
  background: #fafafa;
  border-bottom: 2px solid #e8e8e8;
  min-width: fit-content;
}

.time-column-header {
  width: 80px;
  padding: 16px 8px;
  font-weight: 600;
  color: #1C1C1E;
  text-align: center;
  border-right: 1px solid #e8e8e8;
  background: #f5f5f5;
  position: sticky;
  left: 0;
  z-index: 7; /* Sous le panel de sites (z-index 8) mais au-dessus du contenu */
}

.space-column {
  min-width: 200px;
  max-width: 250px;
  flex: 1;
  border-right: 1px solid #e8e8e8;
}

.space-column:last-child {
  border-right: none;
}

.space-info {
  padding: 12px;
  text-align: center;
}

.space-name {
  font-weight: 600;
  color: #1C1C1E;
  font-size: 14px;
  margin-bottom: 4px;
}

.space-location {
  font-size: 12px;
  color: #8E8E93;
  margin-bottom: 8px;
}

.space-details {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.space-capacity {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6E56CF;
  background: rgba(110, 86, 207, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.space-type {
  color: #8E8E93;
}

.space-amenities {
  display: flex;
  gap: 4px;
}

.space-amenities nz-icon {
  color: #52c41a;
  font-size: 12px;
}

/* Grille du calendrier */
.calendar-grid {
  display: flex;
  min-width: fit-content;
}

.time-slots {
  width: 80px;
  background: #f5f5f5;
  border-right: 1px solid #e8e8e8;
  position: sticky;
  left: 0;
  z-index: 7; /* Sous le panel de sites (z-index 8) mais au-dessus du contenu */
}

.time-slot {
  height: 40px;
  display: flex;
  align-items: flex-start; /* Alignement en haut au lieu du centre */
  justify-content: center;
  padding-top: 4px; /* Petit padding pour ne pas coller au bord */
  font-size: 11px;
  font-weight: 500;
  color: #666;
  border-bottom: 1px solid #e8e8e8;
  position: relative;
}

.time-slot:last-child {
  border-bottom: none;
}

/* Ligne de séparation pour marquer clairement le début de chaque créneau */
.time-slot::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #d9d9d9;
  opacity: 0.7;
}

.time-slot:first-child::before {
  background-color: #8c8c8c;
  opacity: 1;
}

.spaces-grid {
  display: flex;
  flex: 1;
}

.time-cell {
  height: 40px;
  border-bottom: 1px solid #e8e8e8;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-cell:last-child {
  border-bottom: none;
}

/* Blocs de réservation */
.reservation-block {
  position: absolute;
  left: 1px;
  right: 1px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  color: white;
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 2;
  box-sizing: border-box;
}

.reservation-block:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 3;
}

.reservation-content {
  flex: 1;
}

.reservation-title {
  font-weight: 600;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.reservation-member {
  font-size: 10px;
  opacity: 0.9;
  margin-bottom: 2px;
}

.reservation-time {
  font-size: 10px;
  opacity: 0.8;
}



/* Créneaux disponibles */
.available-slot {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  transition: all 0.2s ease;
  touch-action: manipulation; /* Permet le scroll dans toutes les directions */
  user-select: none; /* Empêche la sélection de texte */
}

.available-slot:hover:not(.past-slot) {
  background: rgba(110, 86, 207, 0.05);
  cursor: crosshair;
}

.available-slot:active:not(.past-slot) {
  background: rgba(110, 86, 207, 0.1);
}

/* Slot sélectionné lors du glissement */
.available-slot.selected {
  background: rgba(110, 86, 207, 0.2) !important;
  border: 1px solid rgba(110, 86, 207, 0.4);
  transition: all 0.1s ease;
}

/* Créneaux passés */
.available-slot.past-slot {
  background-color: #f5f5f5;
  border: 1px solid #e8e8e8;
  color: #bfbfbf;
  cursor: not-allowed;
  opacity: 0.6;
}

.available-slot.past-slot .available-text {
  color: #bfbfbf;
  font-style: italic;
}

/* Animation pour le long press */
@keyframes longPressAnimation {
  0% { background: transparent; }
  100% { background: rgba(110, 86, 207, 0.1); }
}

.available-slot:active {
  animation: longPressAnimation 0.5s ease-in-out;
}

.available-text {
  font-size: 11px;
  color: #8E8E93;
  font-weight: 400;
}



.legend-items {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.confirmed {
  background-color: #6E56CF;
}

.legend-color.pending {
  background-color: #B8A9E8;
}

.legend-color.cancelled {
  background-color: #C7C7CC;
}



/* Responsive */
@media (max-width: 768px) {
  /* Header responsive */
  .calendar-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .calendar-legend {
    justify-content: flex-start;
  }

  .legend-items {
    gap: 8px;
  }

  .legend-item {
    font-size: 11px;
  }

  /* Calendrier responsive */
  .calendar-container {
    font-size: 10px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Colonnes d'espaces plus larges pour mobile */
  .space-column {
    min-width: 180px !important;
    max-width: 200px !important;
    flex: none !important;
  }

  /* Cellules de temps plus hautes */
  .time-cell {
    height: 60px !important;
  }

  .time-slot {
    height: 60px !important;
  }

  /* Blocs de réservation adaptés */
  .reservation-block {
    padding: 6px 4px !important;
    font-size: 10px !important;
    border-radius: 6px;
  }

  /* Recalculer la hauteur des réservations pour les nouvelles cellules */
  .reservation-block[style*="height"] {
    /* Force le recalcul de la hauteur basé sur 60px par slot au lieu de 40px */
  }

  .reservation-title {
    font-size: 10px !important;
    line-height: 1.2;
    margin-bottom: 2px;
  }

  .reservation-member {
    font-size: 9px !important;
    margin-bottom: 1px;
  }

  .reservation-time {
    font-size: 8px !important;
  }

  /* Texte disponible */
  .available-text {
    font-size: 10px;
  }

  /* Amélioration tactile pour mobile */
  .available-slot {
    touch-action: manipulation !important; /* Permet le scroll dans toutes les directions */
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    user-select: none !important;
    min-height: 60px !important;
  }

  .time-cell {
    touch-action: manipulation !important; /* Permet le scroll dans toutes les directions */
  }

  /* Header des espaces */
  .space-name {
    font-size: 12px !important;
  }

  .space-location {
    font-size: 10px !important;
  }

  .space-capacity {
    font-size: 10px !important;
  }

  /* Colonne de temps */
  .time-column-header {
    width: 70px !important;
    font-size: 11px;
    position: sticky !important;
    left: 0 !important;
    z-index: 7 !important; /* Sous le panel de sites mais au-dessus du contenu */ /* Sous le panel de sites mais au-dessus du contenu */
  }

  .time-slots {
    width: 70px !important;
    position: sticky !important;
    left: 0 !important;
    z-index: 5 !important;
  }

  .time-slot {
    font-size: 9px !important;
  }
}
