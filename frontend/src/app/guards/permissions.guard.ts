import { Injectable, inject } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';

@Injectable({
  providedIn: 'root'
})
export class PermissionsGuard implements CanActivate {

  private authService = inject(AuthService);
  private router = inject(Router);
  private message = inject(NzMessageService);
  private modal = inject(NzModalService);

  canActivate(route: ActivatedRouteSnapshot): boolean {
    if (!this.authService.isAuthenticated()) {
      this.router.navigate(['/tenant-selection']);
      return false;
    }

    const requiredPermissions = route.data['permissions'] as string[];

    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true; // Pas de permissions requises
    }

    // Vérifier si l'utilisateur a au moins une des permissions requises
    const hasPermission = this.checkPermissions(requiredPermissions);

    if (!hasPermission) {
      this.showUpgradeModal(route);
      this.router.navigate(['/welcome']);
      return false;
    }

    return true;
  }

  private checkPermissions(requiredPermissions: string[]): boolean {
    const userRoles = this.authService.getResourceRoles();
    console.log('Checking permissions:', requiredPermissions, 'for user roles:', userRoles);

    // Mapping des permissions aux rôles
    const permissionRoleMap: { [key: string]: string[] } = {
      'CAN_MANAGE_SUBSCRIPTIONS': ['STARTER_PLAN', 'PROFESSIONAL_PLAN', 'ENTREPRISE_PLAN'],
      'CAN_ACCESS_BILLING': ['STARTER_PLAN', 'PROFESSIONAL_PLAN', 'ENTREPRISE_PLAN'],
      'CAN_ACCESS_STATISTICS': ['STARTER_PLAN', 'PROFESSIONAL_PLAN', 'ENTREPRISE_PLAN'],
      'CAN_MANAGE_MULTI_SITE': ['PROFESSIONAL_PLAN', 'ENTREPRISE_PLAN']
    };

    const hasPermission = requiredPermissions.some(permission => {
      const allowedRoles = permissionRoleMap[permission];
      if (!allowedRoles) {
        return true; // Permission non définie = accès autorisé
      }
      return allowedRoles.some(role => userRoles.includes(role));
    });

    console.log('Permission check result:', hasPermission);
    return hasPermission;
  }

  private showUpgradeModal(route: ActivatedRouteSnapshot): void {
    const featureInfo = this.getFeatureInfo(route);
    const currentPlan = this.getCurrentPlanName();

    this.modal.warning({
      nzTitle: `🚀 Fonctionnalité ${featureInfo.name} non disponible`,
      nzContent: `
        <div style="margin: 16px 0;">
          <p><strong>Votre plan actuel :</strong> ${currentPlan}</p>
          <p><strong>Cette fonctionnalité est disponible avec :</strong></p>
          <ul style="margin: 8px 0; padding-left: 20px;">
            ${featureInfo.availablePlans.map(plan => `<li>${plan}</li>`).join('')}
          </ul>
          <p style="margin-top: 16px; color: #1890ff;">
            <strong>✨ Passez à un plan supérieur pour débloquer cette fonctionnalité et bien plus encore !</strong>
          </p>
        </div>
      `,
      nzOkText: 'Voir les plans',
      nzCancelText: 'Plus tard',
      nzOnOk: () => {
        // Ouvrir la page des plans dans un nouvel onglet
        window.open('https://workeem.ma/#pricing', '_blank');
      },
      nzWidth: 500,
      nzCentered: true
    });
  }

  private getFeatureInfo(route: ActivatedRouteSnapshot): { name: string, availablePlans: string[] } {
    const path = route.routeConfig?.path || '';

    const featureMap: { [key: string]: { name: string, availablePlans: string[] } } = {
      'subscriptions': {
        name: 'Gestion des Abonnements',
        availablePlans: ['STARTER', 'PROFESSIONAL', 'ENTREPRISE']
      },
      'billing': {
        name: 'Facturation & Paiements',
        availablePlans: ['STARTER', 'PROFESSIONAL', 'ENTREPRISE']
      },
      'statistics': {
        name: 'Statistiques Avancées',
        availablePlans: ['STARTER', 'PROFESSIONAL', 'ENTREPRISE']
      }
    };

    return featureMap[path] || {
      name: 'cette fonctionnalité',
      availablePlans: ['STARTER', 'PROFESSIONAL', 'ENTREPRISE']
    };
  }

  private getCurrentPlanName(): string {
    const userRoles = this.authService.getResourceRoles();

    if (userRoles.includes('BASIC_PLAN')) return 'BASIC';
    if (userRoles.includes('STARTER_PLAN')) return 'STARTER';
    if (userRoles.includes('PROFESSIONAL_PLAN')) return 'PROFESSIONAL';
    if (userRoles.includes('ENTREPRISE_PLAN')) return 'ENTREPRISE';

    return 'Non défini';
  }
}
