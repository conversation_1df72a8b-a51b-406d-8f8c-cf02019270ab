export enum SubscriptionType {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  HALF_YEARLY = 'half_yearly',
  YEARLY = 'yearly',
  FLEXIBLE = 'flexible'
}

export enum SubscriptionStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  EXPIRED = 'expired',
  SUSPENDED = 'suspended'
}

export enum MembershipType {
  STUDENT = 'STUDENT',
  PROFESSIONAL = 'PROFESSIONAL',
  COMPANY = 'COMPANY'
}

export interface TimeSlot {
  start: string; // Format HH:mm
  end: string;   // Format HH:mm
}

export interface SubscriptionRights {
  maxReservationsPerDay: number;
  maxReservationsPerWeek: number;
  maxReservationsPerMonth: number;
  allowedTimeSlots: TimeSlot[];
  allowedDays: string[]; // ['monday', 'tuesday', etc.]
  includedRooms: string[]; // IDs des salles incluses
  canBookMeetingRooms: boolean;
  canAccessPremiumAreas: boolean;
  maxConsecutiveHours: number;
  advanceBookingDays: number; // Nombre de jours à l'avance pour réserver
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  type: SubscriptionType;
  membershipTypes: MembershipType[]; // Changé pour permettre plusieurs types
  price: number;
  currency: string;
  duration: number; // en jours
  rights: SubscriptionRights;
  isActive: boolean;
  features: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface UserSubscription {
  id: string;
  userId: string;
  planId: string;
  plan: SubscriptionPlan;
  status: SubscriptionStatus;
  startDate: Date;
  endDate: Date;
  autoRenew: boolean;
  paymentMethod: string;
  lastPaymentDate?: Date;
  nextPaymentDate?: Date;
  remainingReservations: {
    daily: number;
    weekly: number;
    monthly: number;
  };
  usageStats: {
    totalReservations: number;
    totalHours: number;
    lastUsed?: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateSubscriptionPlanRequest {
  name: string;
  description: string;
  type: SubscriptionType;
  membershipTypes: MembershipType[]; // Changé pour permettre plusieurs types
  price: number;
  duration: number;
  rights: SubscriptionRights;
  features: string[];
}

export interface UpdateSubscriptionPlanRequest extends Partial<CreateSubscriptionPlanRequest> {
  isActive?: boolean;
}

export interface AssignSubscriptionRequest {
  userId: string;
  planId: string;
  startDate: Date;
  autoRenew: boolean;
  paymentMethod: string;
}

export interface SubscriptionAlert {
  id: string;
  userId: string;
  subscriptionId: string;
  type: 'expiration' | 'payment_due' | 'usage_limit' | 'renewal_failed';
  message: string;
  severity: 'info' | 'warning' | 'error';
  isRead: boolean;
  createdAt: Date;
}
