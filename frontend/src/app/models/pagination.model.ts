/**
 * Interface pour les paramètres de pagination envoyés au backend
 */
export interface PageRequest {
  page: number; // Page courante (0-based)
  size: number; // Taille de la page
  sortBy?: string; // Champ de tri
  sortDirection?: 'ASC' | 'DESC'; // Direction du tri
}

/**
 * Interface pour la réponse paginée du backend
 */
export interface PageResponse<T> {
  content: T[]; // Contenu de la page
  page: number; // Page courante (0-based)
  size: number; // Taille de la page
  totalElements: number; // Nombre total d'éléments
  totalPages: number; // Nombre total de pages
  first: boolean; // Est-ce la première page ?
  last: boolean; // Est-ce la dernière page ?
  empty: boolean; // La page est-elle vide ?
  numberOfElements: number; // Nombre d'éléments dans cette page
}

/**
 * Classe utilitaire pour créer des objets PageRequest
 */
export class PageRequestBuilder {
  private pageRequest: PageRequest = {
    page: 0,
    size: 10,
    sortDirection: 'ASC'
  };

  static create(): PageRequestBuilder {
    return new PageRequestBuilder();
  }

  page(page: number): PageRequestBuilder {
    this.pageRequest.page = Math.max(0, page);
    return this;
  }

  size(size: number): PageRequestBuilder {
    this.pageRequest.size = Math.max(1, Math.min(100, size));
    return this;
  }

  sortBy(field: string): PageRequestBuilder {
    this.pageRequest.sortBy = field;
    return this;
  }

  sortDirection(direction: 'ASC' | 'DESC'): PageRequestBuilder {
    this.pageRequest.sortDirection = direction;
    return this;
  }

  build(): PageRequest {
    return { ...this.pageRequest };
  }
}

/**
 * Interface pour l'état de pagination dans les composants
 */
export interface PaginationState {
  currentPage: number; // Page courante (1-based pour l'affichage)
  pageSize: number; // Taille de la page
  total: number; // Nombre total d'éléments
  loading: boolean; // Indicateur de chargement
}

/**
 * Options de pagination par défaut
 */
export const DEFAULT_PAGINATION: PageRequest = {
  page: 0,
  size: 10,
  sortDirection: 'ASC'
};

/**
 * Tailles de page disponibles
 */
export const PAGE_SIZE_OPTIONS = [10, 20, 50, 100];
