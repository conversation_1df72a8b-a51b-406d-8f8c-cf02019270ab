import { Injectable } from '@angular/core';
import { NgxPermissionsService } from 'ngx-permissions';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class PermissionsService {

  constructor(
    private permissionsService: NgxPermissionsService,
    private authService: AuthService
  ) {}

  /**
   * Initialise les permissions basées sur les rôles de l'utilisateur
   */
  initializePermissions(): void {
    if (!this.authService.isAuthenticated()) {
      this.permissionsService.flushPermissions();
      return;
    }

    const resourceRoles = this.authService.getResourceRoles();
    console.log('Initializing permissions for roles:', resourceRoles);

    // Charger les rôles de base
    this.permissionsService.loadPermissions(resourceRoles);

    // Ajouter des permissions dérivées basées sur les rôles
    this.addDerivedPermissions(resourceRoles);
  }

  /**
   * Ajoute des permissions dérivées basées sur les rôles
   */
  private addDerivedPermissions(roles: string[]): void {
    const permissions: string[] = [...roles];

    // Permissions pour les fonctionnalités de base (tous les plans)
    permissions.push('CAN_MANAGE_SPACES');
    permissions.push('CAN_MANAGE_MEMBERS');
    permissions.push('CAN_MANAGE_RESERVATIONS');

    // Permissions pour les abonnements (sauf BASIC_PLAN)
    if (this.hasAnyRole(roles, ['STARTER_PLAN', 'PROFESSIONAL_PLAN', 'ENTREPRISE_PLAN'])) {
      permissions.push('CAN_MANAGE_SUBSCRIPTIONS');
      permissions.push('CAN_ACCESS_BILLING');
      permissions.push('CAN_ACCESS_STATISTICS');
    }

    // Permissions pour le multi-site (PROFESSIONAL_PLAN et ENTREPRISE_PLAN)
    if (this.hasAnyRole(roles, ['PROFESSIONAL_PLAN', 'ENTREPRISE_PLAN'])) {
      permissions.push('CAN_MANAGE_MULTI_SITE');
    }

    // Charger toutes les permissions
    this.permissionsService.loadPermissions(permissions);
  }

  /**
   * Vérifie si l'utilisateur a au moins un des rôles spécifiés
   */
  private hasAnyRole(userRoles: string[], requiredRoles: string[]): boolean {
    return requiredRoles.some(role => userRoles.includes(role));
  }

  /**
   * Vérifie si l'utilisateur a une permission spécifique
   */
  hasPermission(permission: string): boolean {
    return this.permissionsService.getPermission(permission) !== undefined;
  }

  /**
   * Méthodes utilitaires pour vérifier les permissions spécifiques
   */
  canManageSpaces(): boolean {
    return this.hasPermission('CAN_MANAGE_SPACES');
  }

  canManageMembers(): boolean {
    return this.hasPermission('CAN_MANAGE_MEMBERS');
  }

  canManageReservations(): boolean {
    return this.hasPermission('CAN_MANAGE_RESERVATIONS');
  }

  canManageSubscriptions(): boolean {
    return this.hasPermission('CAN_MANAGE_SUBSCRIPTIONS');
  }

  canAccessBilling(): boolean {
    return this.hasPermission('CAN_ACCESS_BILLING');
  }

  canAccessStatistics(): boolean {
    return this.hasPermission('CAN_ACCESS_STATISTICS');
  }

  canManageMultiSite(): boolean {
    return this.hasPermission('CAN_MANAGE_MULTI_SITE');
  }
}
