import { Injectable, signal } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of, delay, map, catchError } from 'rxjs';
import {
  SubscriptionPlan,
  UserSubscription,
  CreateSubscriptionPlanRequest,
  UpdateSubscriptionPlanRequest,
  AssignSubscriptionRequest,
  SubscriptionAlert,
  SubscriptionType,
  SubscriptionStatus,
  MembershipType
} from '../models/subscription.model';
import { PageRequest, PageResponse, PageRequestBuilder } from '../models/pagination.model';
import { SiteContextService } from './site-context.service';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class SubscriptionService {
  private readonly apiUrl = `${environment.apiUrl}/api/subscriptions`;

  // ✅ Données supprimées - maintenant chargées depuis le backend


  // Signaux pour la réactivité
  private plansSignal = signal<SubscriptionPlan[]>([]);
  private userSubscriptionsSignal = signal<UserSubscription[]>([]);
  private loadingSignal = signal<boolean>(false);

  constructor(
    private http: HttpClient,
    private siteContextService: SiteContextService
  ) {
    // Les plans seront chargés à la demande via getPlans()
  }

  // Méthode utilitaire pour récupérer le siteId actuel
  private getCurrentSiteId(): string {
    return this.siteContextService.getCurrentSiteId()?.toString() || '1';
  }

  // Getters pour les templates
  get loading() { return this.loadingSignal(); }

  // Charger les plans d'abonnement depuis le backend
  private loadPlansFromBackend(siteId: string = '1'): void {
    this.loadingSignal.set(true);

    const params = new HttpParams()
      .set('siteId', siteId)
      .set('active', 'true');

    this.http.get<any[]>(this.apiUrl, { params }).pipe(
      map(subscriptionDataList => {
        return subscriptionDataList.map(subscriptionData => this.mapBackendDataToSubscriptionPlan(subscriptionData));
      }),
      catchError(error => {
        console.error('Error loading subscription plans from backend:', error);
        return of([]);
      })
    ).subscribe({
      next: (plans) => {
        this.plansSignal.set(plans);
        this.loadingSignal.set(false);
      },
      error: () => {
        this.loadingSignal.set(false);
      }
    });
  }

  // Gestion des plans d'abonnement (version non paginée - pour compatibilité)
  getPlans(): Observable<SubscriptionPlan[]> {
    // Utiliser la méthode paginée en interne pour récupérer tous les plans
    const pageRequest = PageRequestBuilder.create()
      .page(0)
      .size(100) // Récupérer beaucoup de plans
      .sortBy('name')
      .sortDirection('ASC')
      .build();

    return this.getPlansPaginated(pageRequest, true).pipe(
      map(pageResponse => {
        // Retourner seulement le contenu (tableau de plans)
        return pageResponse.content;
      }),
      catchError(error => {
        console.error('Error loading subscription plans:', error);
        // En cas d'erreur, retourner les données locales si disponibles
        return of(this.plansSignal());
      })
    );
  }

  // Récupérer les plans d'abonnement avec pagination
  getPlansPaginated(pageRequest: PageRequest, active?: boolean, memberType?: string, searchTerm?: string): Observable<PageResponse<SubscriptionPlan>> {
    this.loadingSignal.set(true);

    let params = new HttpParams()
      .set('siteId', this.getCurrentSiteId())
      .set('page', pageRequest.page.toString())
      .set('size', pageRequest.size.toString())
      .set('sortDirection', pageRequest.sortDirection || 'ASC');

    if (pageRequest.sortBy) {
      params = params.set('sortBy', pageRequest.sortBy);
    }

    if (active !== undefined) {
      params = params.set('active', active.toString());
    }

    if (memberType) {
      params = params.set('memberType', memberType);
    }

    if (searchTerm && searchTerm.trim()) {
      params = params.set('search', searchTerm.trim());
    }

    console.log('Sending request to', this.apiUrl, 'with params:', params.toString()); // Debug

    return this.http.get<any>(this.apiUrl, { params }).pipe(
      map((response: any) => {
        console.log('Raw backend response:', response); // Debug
        console.log('Response type:', typeof response, 'Is array:', Array.isArray(response)); // Debug

        // Si la réponse est un tableau (ancienne API), on la convertit en PageResponse
        if (Array.isArray(response)) {
          console.warn('Backend returned array instead of PageResponse, converting...'); // Debug
          const pageResponse: PageResponse<any> = {
            content: response,
            page: pageRequest.page,
            size: pageRequest.size,
            totalElements: response.length,
            totalPages: Math.ceil(response.length / pageRequest.size),
            first: pageRequest.page === 0,
            last: pageRequest.page >= Math.ceil(response.length / pageRequest.size) - 1,
            empty: response.length === 0,
            numberOfElements: response.length
          };
          return pageResponse;
        }

        // Si c'est déjà un objet PageResponse
        if (response && response.content && Array.isArray(response.content)) {
          return response as PageResponse<any>;
        }

        // Structure invalide
        console.error('Invalid response structure:', response);
        throw new Error('Invalid response structure from backend');
      }),
      map((pageResponse: PageResponse<any>) => {
        // Convertir les données backend en plans frontend
        const plans = pageResponse.content.map((subscriptionData: any) => this.mapBackendDataToSubscriptionPlan(subscriptionData));

        // Mettre à jour le signal local avec les plans de la page courante
        this.plansSignal.set(plans);
        this.loadingSignal.set(false);

        // Retourner la réponse paginée avec les plans convertis
        return {
          ...pageResponse,
          content: plans
        };
      }),
      catchError(error => {
        console.error('Error loading paginated subscription plans:', error);
        this.loadingSignal.set(false);
        // Retourner une page vide en cas d'erreur
        return of({
          content: [],
          page: pageRequest.page,
          size: pageRequest.size,
          totalElements: 0,
          totalPages: 0,
          first: true,
          last: true,
          empty: true,
          numberOfElements: 0
        });
      })
    );
  }

  getPlanById(id: string): Observable<SubscriptionPlan | null> {
    const plan = this.plansSignal().find(p => p.id === id) || null;
    return of(plan).pipe(delay(200));
  }

  createPlan(request: CreateSubscriptionPlanRequest): Observable<SubscriptionPlan> {
    // Mapper la requête frontend vers le format backend
    const backendRequest = {
      name: request.name,
      description: request.description,
      price: request.price,
      currency: 'MAD',
      durationDays: request.duration,
      memberType: this.mapMembershipTypeToBackend(request.membershipTypes[0]), // Prendre le premier type
      features: request.features.join(','),
      maxReservationsPerDay: request.rights.maxReservationsPerDay,
      maxReservationsPerWeek: request.rights.maxReservationsPerWeek,
      maxReservationsPerMonth: request.rights.maxReservationsPerMonth,
      maxConsecutiveHours: request.rights.maxConsecutiveHours,
      advanceBookingDays: request.rights.advanceBookingDays,
      canBookMeetingRooms: request.rights.canBookMeetingRooms,
      canAccessPremiumAreas: request.rights.canAccessPremiumAreas,
      isActive: true
    };

    const params = new HttpParams().set('siteId', '1'); // TODO: Récupérer depuis le contexte

    return this.http.post<any>(this.apiUrl, backendRequest, { params }).pipe(
      map(subscriptionData => {
        const newPlan = this.mapBackendDataToSubscriptionPlan(subscriptionData);
        // Mettre à jour la liste locale
        const currentPlans = this.plansSignal();
        this.plansSignal.set([...currentPlans, newPlan]);
        return newPlan;
      }),
      catchError(error => {
        console.error('Error creating subscription plan:', error);
        // Fallback vers la création locale
        const newPlan: SubscriptionPlan = {
          id: Date.now().toString(),
          ...request,
          currency: 'MAD',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        const currentPlans = this.plansSignal();
        this.plansSignal.set([...currentPlans, newPlan]);
        return of(newPlan);
      })
    );
  }

  updatePlan(id: string, request: UpdateSubscriptionPlanRequest): Observable<SubscriptionPlan> {
    const currentPlans = this.plansSignal();
    const planIndex = currentPlans.findIndex(p => p.id === id);

    if (planIndex === -1) {
      throw new Error('Plan non trouvé');
    }

    const updatedPlan = {
      ...currentPlans[planIndex],
      ...request,
      updatedAt: new Date()
    };

    const updatedPlans = [...currentPlans];
    updatedPlans[planIndex] = updatedPlan;
    this.plansSignal.set(updatedPlans);

    return of(updatedPlan).pipe(delay(500));
  }

  deletePlan(id: string): Observable<void> {
    const currentPlans = this.plansSignal();
    const filteredPlans = currentPlans.filter(p => p.id !== id);
    this.plansSignal.set(filteredPlans);

    return of(void 0).pipe(delay(300));
  }

  // Gestion des abonnements utilisateurs
  getUserSubscriptions(): Observable<UserSubscription[]> {
    return of(this.userSubscriptionsSignal()).pipe(delay(300));
  }

  getUserSubscriptionById(id: string): Observable<UserSubscription | null> {
    const subscription = this.userSubscriptionsSignal().find(s => s.id === id) || null;
    return of(subscription).pipe(delay(200));
  }

  assignSubscription(request: AssignSubscriptionRequest): Observable<UserSubscription> {
    const plan = this.plansSignal().find(p => p.id === request.planId);
    if (!plan) {
      throw new Error('Plan non trouvé');
    }

    const endDate = new Date(request.startDate);
    endDate.setDate(endDate.getDate() + plan.duration);

    const newSubscription: UserSubscription = {
      id: Date.now().toString(),
      userId: request.userId,
      planId: request.planId,
      plan: plan,
      status: SubscriptionStatus.ACTIVE,
      startDate: request.startDate,
      endDate: endDate,
      autoRenew: request.autoRenew,
      paymentMethod: request.paymentMethod,
      nextPaymentDate: endDate,
      remainingReservations: {
        daily: plan.rights.maxReservationsPerDay,
        weekly: plan.rights.maxReservationsPerWeek,
        monthly: plan.rights.maxReservationsPerMonth
      },
      usageStats: {
        totalReservations: 0,
        totalHours: 0
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const currentSubscriptions = this.userSubscriptionsSignal();
    this.userSubscriptionsSignal.set([...currentSubscriptions, newSubscription]);

    return of(newSubscription).pipe(delay(500));
  }

  // Alertes et notifications
  getExpiringSubscriptions(days: number = 7): Observable<UserSubscription[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() + days);

    return this.getUserSubscriptions().pipe(
      map(subscriptions =>
        subscriptions.filter(sub =>
          sub.status === SubscriptionStatus.ACTIVE &&
          sub.endDate <= cutoffDate
        )
      )
    );
  }

  getSubscriptionAlerts(): Observable<SubscriptionAlert[]> {
    // Mock des alertes
    const alerts: SubscriptionAlert[] = [
      {
        id: '1',
        userId: '1',
        subscriptionId: '1',
        type: 'expiration',
        message: 'Votre abonnement expire dans 3 jours',
        severity: 'warning',
        isRead: false,
        createdAt: new Date()
      }
    ];

    return of(alerts).pipe(delay(200));
  }

  // Statistiques
  getSubscriptionStats(): Observable<any> {
    const plans = this.plansSignal();
    const subscriptions = this.userSubscriptionsSignal();

    const stats = {
      totalPlans: plans.length,
      activePlans: plans.filter(p => p.isActive).length,
      totalSubscriptions: subscriptions.length,
      activeSubscriptions: subscriptions.filter(s => s.status === SubscriptionStatus.ACTIVE).length,
      expiringSubscriptions: subscriptions.filter(s => {
        const daysUntilExpiry = Math.ceil((s.endDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
        return daysUntilExpiry <= 7 && daysUntilExpiry > 0;
      }).length,
      monthlyRevenue: subscriptions
        .filter(s => s.status === SubscriptionStatus.ACTIVE)
        .reduce((sum, s) => sum + s.plan.price, 0)
    };

    return of(stats).pipe(delay(300));
  }

  // Mapper les données backend vers un SubscriptionPlan frontend
  private mapBackendDataToSubscriptionPlan(subscriptionData: any): SubscriptionPlan {
    // Déterminer le type d'abonnement basé sur la durée
    let type: SubscriptionType;
    if (subscriptionData.durationDays === 1) {
      type = SubscriptionType.DAILY;
    } else if (subscriptionData.durationDays <= 7) {
      type = SubscriptionType.WEEKLY;
    } else if (subscriptionData.durationDays <= 31) {
      type = SubscriptionType.MONTHLY;
    } else if (subscriptionData.durationDays <= 183) { // ~6 mois
      type = SubscriptionType.HALF_YEARLY;
    } else if (subscriptionData.durationDays <= 365) { // ~1 an
      type = SubscriptionType.YEARLY;
    } else {
      type = SubscriptionType.FLEXIBLE; // Pour les durées très longues
    }

    // Mapper le memberType vers MembershipType
    const membershipTypes: MembershipType[] = [];
    if (subscriptionData.memberType) {
      switch (subscriptionData.memberType) {
        case 'STUDENT':
          membershipTypes.push(MembershipType.STUDENT);
          break;
        case 'PROFESSIONAL':
          membershipTypes.push(MembershipType.PROFESSIONAL);
          break;
        case 'COMPANY':
          membershipTypes.push(MembershipType.COMPANY);
          break;
      }
    } else {
      // Si pas de type spécifique, compatible avec tous
      membershipTypes.push(MembershipType.STUDENT, MembershipType.PROFESSIONAL, MembershipType.COMPANY);
    }

    return {
      id: subscriptionData.subscriptionId.toString(),
      name: subscriptionData.name,
      description: subscriptionData.description || '',
      type: type,
      membershipTypes: membershipTypes,
      price: subscriptionData.price,
      currency: subscriptionData.currency || 'MAD',
      duration: subscriptionData.durationDays,
      rights: {
        maxReservationsPerDay: subscriptionData.maxReservationsPerDay || 3,
        maxReservationsPerWeek: subscriptionData.maxReservationsPerWeek || 15,
        maxReservationsPerMonth: subscriptionData.maxReservationsPerMonth || 60,
        allowedTimeSlots: [{ start: '08:00', end: '18:00' }], // TODO: À implémenter dans le backend
        allowedDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'], // TODO: À implémenter
        includedRooms: [], // TODO: À implémenter avec les espaces
        canBookMeetingRooms: subscriptionData.canBookMeetingRooms || false,
        canAccessPremiumAreas: subscriptionData.canAccessPremiumAreas || false,
        maxConsecutiveHours: subscriptionData.maxConsecutiveHours || 8,
        advanceBookingDays: subscriptionData.advanceBookingDays || 30
      },
      isActive: subscriptionData.isActive,
      features: subscriptionData.features ? subscriptionData.features.split(',') : [],
      createdAt: subscriptionData.createdAt ? new Date(subscriptionData.createdAt) : new Date(),
      updatedAt: subscriptionData.updatedAt ? new Date(subscriptionData.updatedAt) : new Date()
    };
  }

  // Mapper MembershipType vers le format backend
  private mapMembershipTypeToBackend(membershipType: MembershipType): string | null {
    switch (membershipType) {
      case MembershipType.STUDENT:
        return 'STUDENT';
      case MembershipType.PROFESSIONAL:
        return 'PROFESSIONAL';
      case MembershipType.COMPANY:
        return 'COMPANY';
      default:
        return null;
    }
  }
}
