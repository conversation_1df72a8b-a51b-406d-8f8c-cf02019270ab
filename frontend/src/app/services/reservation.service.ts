import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { Reservation, ReservationFilter } from '../models/reservation.model';
import { SiteContextService } from './site-context.service';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ReservationService {
  private readonly apiUrl = `${environment.apiUrl}/api/reservations`;

  private reservationsSubject = new BehaviorSubject<Reservation[]>([]);
  public reservations$ = this.reservationsSubject.asObservable();

  constructor(
    private http: HttpClient,
    private siteContext: SiteContextService
  ) {}

  /**
   * Get current site ID from context
   */
  private getCurrentSiteId(): string {
    const currentSite = this.siteContext.getCurrentSite();
    if (!currentSite || !currentSite.id) {
      console.warn('⚠️ No site selected in context, using fallback');
      return '1'; // Fallback to site ID 1
    }
    return currentSite.id;
  }

  // Méthodes CRUD pour les réservations
  getReservations(filters?: ReservationFilter): Observable<Reservation[]> {
    let params = new HttpParams().set('siteId', this.getCurrentSiteId());

    if (filters?.spaceId) {
      params = params.set('spaceId', filters.spaceId);
    }
    if (filters?.status) {
      params = params.set('status', filters.status.toUpperCase());
    }
    if (filters?.dateRange?.start) {
      params = params.set('startDate', filters.dateRange.start.toISOString());
    }
    if (filters?.dateRange?.end) {
      params = params.set('endDate', filters.dateRange.end.toISOString());
    }

    return this.http.get<any[]>(`${this.apiUrl}`, { params }).pipe(
      map(response => this.mapReservationsFromBackend(response)),
      catchError(error => {
        console.error('Error fetching reservations:', error);
        return throwError(() => error);
      })
    );
  }

  getReservationById(id: string): Observable<Reservation | null> {
    return this.http.get<any>(`${this.apiUrl}/${id}`).pipe(
      map(response => this.mapReservationFromBackend(response)),
      catchError(error => {
        console.error('Error fetching reservation by id:', error);
        return throwError(() => error);
      })
    );
  }

  getReservationsBySpace(spaceId: string): Observable<Reservation[]> {
    const params = new HttpParams()
      .set('siteId', this.getCurrentSiteId());

    return this.http.get<any[]>(`${this.apiUrl}/by-space/${spaceId}`, { params }).pipe(
      map(response => this.mapReservationsFromBackend(response)),
      catchError(error => {
        console.error('Error fetching reservations by space:', error);
        return throwError(() => error);
      })
    );
  }

  getReservationsByUser(userId: string): Observable<Reservation[]> {
    const params = new HttpParams()
      .set('siteId', this.getCurrentSiteId());

    return this.http.get<any[]>(`${this.apiUrl}/by-user/${userId}`, { params }).pipe(
      map(response => this.mapReservationsFromBackend(response)),
      catchError(error => {
        console.error('Error fetching reservations by user:', error);
        return throwError(() => error);
      })
    );
  }

  createReservation(reservation: Partial<Reservation>): Observable<Reservation> {
    const params = new HttpParams().set('siteId', this.getCurrentSiteId());
    const backendData = this.mapCreateRequestToBackend(reservation);

    return this.http.post<any>(`${this.apiUrl}`, backendData, { params }).pipe(
      map(response => this.mapReservationFromBackend(response)),
      catchError(error => {
        console.error('Error creating reservation:', error);
        return throwError(() => error);
      })
    );
  }

  updateReservation(id: string, reservation: Partial<Reservation>): Observable<Reservation> {
    const params = new HttpParams().set('siteId', this.getCurrentSiteId());
    const backendData = this.mapUpdateRequestToBackend(reservation);

    return this.http.put<any>(`${this.apiUrl}/${id}`, backendData, { params }).pipe(
      map(response => this.mapReservationFromBackend(response)),
      catchError(error => {
        console.error('Error updating reservation:', error);
        return throwError(() => error);
      })
    );
  }

  cancelReservation(id: string): Observable<Reservation> {
    return this.http.patch<any>(`${this.apiUrl}/${id}/cancel`, {}).pipe(
      map(response => this.mapReservationFromBackend(response)),
      catchError(error => {
        console.error('Error cancelling reservation:', error);
        return throwError(() => error);
      })
    );
  }

  updateReservationStatus(id: string, status: string): Observable<Reservation> {
    const params = new HttpParams().set('status', status);

    return this.http.patch<any>(`${this.apiUrl}/${id}/status`, {}, { params }).pipe(
      map(response => this.mapReservationFromBackend(response)),
      catchError(error => {
        console.error('Error updating reservation status:', error);
        return throwError(() => error);
      })
    );
  }

  deleteReservation(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`).pipe(
      catchError(error => {
        console.error('Error deleting reservation:', error);
        return throwError(() => error);
      })
    );
  }

  checkConflicts(spaceId: string, startTime: Date, endTime: Date, excludeReservationId?: string): Observable<boolean> {
    let params = new HttpParams()
      .set('siteId', this.getCurrentSiteId())
      .set('spaceId', spaceId)
      .set('startTime', startTime.toISOString())
      .set('endTime', endTime.toISOString());

    if (excludeReservationId) {
      params = params.set('excludeReservationId', excludeReservationId);
    }

    return this.http.get<boolean>(`${this.apiUrl}/check-conflicts`, { params }).pipe(
      catchError(error => {
        console.error('Error checking conflicts:', error);
        return throwError(() => error);
      })
    );
  }

  // Méthodes de mapping entre frontend et backend
  private mapReservationsFromBackend(backendReservations: any[]): Reservation[] {
    return backendReservations.map(reservation => this.mapReservationFromBackend(reservation));
  }

  private mapReservationFromBackend(backendReservation: any): Reservation {
    return {
      id: backendReservation.id?.toString() || '',
      spaceId: backendReservation.spaceId?.toString() || '',
      spaceName: backendReservation.spaceName || '',
      memberId: backendReservation.memberId?.toString() || '',
      userName: backendReservation.memberName || '',
      userEmail: backendReservation.memberEmail || '',
      startTime: new Date(backendReservation.startTime),
      endTime: new Date(backendReservation.endTime),
      purpose: backendReservation.purpose || '',
      status: backendReservation.status?.toLowerCase() || 'pending',
      numberOfPeople: backendReservation.numberOfPeople || 1,
      notes: backendReservation.notes || '',
      recurrence: backendReservation.recurrence || 'none',
      createdAt: new Date(backendReservation.createdAt),
      updatedAt: new Date(backendReservation.updatedAt)
    };
  }

  private mapCreateRequestToBackend(reservation: Partial<Reservation>): any {
    // Validation des dates
    if (!reservation.startTime || !reservation.endTime) {
      throw new Error('Start time and end time are required');
    }

    const startTime = reservation.startTime instanceof Date ? reservation.startTime : new Date(reservation.startTime);
    const endTime = reservation.endTime instanceof Date ? reservation.endTime : new Date(reservation.endTime);

    if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
      throw new Error('Invalid start time or end time');
    }

    const requestData: any = {
      spaceId: parseInt(reservation.spaceId || '0'),
      memberId: reservation.memberId ? parseInt(reservation.memberId.toString()) : null,
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      purpose: reservation.purpose,
      status: reservation.status?.toUpperCase(),
      numberOfPeople: reservation.numberOfPeople,
      notes: reservation.notes,
      recurrence: reservation.recurrence?.toUpperCase(),
      totalCost: 0 // À calculer côté backend si nécessaire
    };

    // Ajouter les informations du nouveau member si nécessaire
    if ((reservation as any).firstName) {
      requestData.firstName = (reservation as any).firstName;
    }
    if ((reservation as any).lastName) {
      requestData.lastName = (reservation as any).lastName;
    }
    if ((reservation as any).email) {
      requestData.email = (reservation as any).email;
    }
    if ((reservation as any).phone) {
      requestData.phone = (reservation as any).phone;
    }
    if ((reservation as any).company) {
      requestData.company = (reservation as any).company;
    }

    return requestData;
  }

  private mapUpdateRequestToBackend(reservation: Partial<Reservation>): any {
    return this.mapCreateRequestToBackend(reservation);
  }

  /**
   * Get upcoming reservations count (confirmed reservations starting from now)
   */
  getUpcomingReservationsCount(siteId: number): Observable<{ confirmed: number, total: number }> {
    const params = new HttpParams()
      .set('siteId', siteId.toString())
      .set('startDate', new Date().toISOString());

    return this.http.get<any[]>(`${this.apiUrl}`, { params }).pipe(
      map(reservations => {
        const now = new Date();
        const upcomingReservations = reservations.filter(r => new Date(r.startTime) >= now);
        const confirmedUpcoming = upcomingReservations.filter(r => r.status === 'confirmed');

        return {
          confirmed: confirmedUpcoming.length,
          total: upcomingReservations.length
        };
      }),
      catchError(error => {
        console.error('Error fetching upcoming reservations count:', error);
        return of({ confirmed: 0, total: 0 });
      })
    );
  }

  /**
   * Get current occupancy (active reservations happening now)
   */
  getCurrentOccupancy(siteId: number): Observable<number> {
    const now = new Date();
    const params = new HttpParams()
      .set('siteId', siteId.toString())
      .set('startDate', now.toISOString())
      .set('endDate', now.toISOString());

    return this.http.get<any[]>(`${this.apiUrl}`, { params }).pipe(
      map(reservations => {
        // Compter les réservations actives en ce moment
        return reservations.filter(r => {
          const startTime = new Date(r.startTime);
          const endTime = new Date(r.endTime);
          return startTime <= now && endTime > now && r.status === 'confirmed';
        }).reduce((total, r) => total + (r.numberOfPeople || 1), 0);
      }),
      catchError(error => {
        console.error('Error fetching current occupancy:', error);
        return of(0);
      })
    );
  }
}
