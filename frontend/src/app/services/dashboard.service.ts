import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface DashboardStats {
  // Statistiques des membres
  totalMembers?: number;
  activeMembers?: number;

  // Statistiques des espaces
  totalSpaces?: number;
  availableSpaces?: number;

  // Statistiques des réservations
  totalReservations?: number;
  activeReservations?: number;
  upcomingReservations?: number;
  todayReservations?: number;

  // Statistiques financières (optionnelles selon les permissions)
  monthlyRevenue?: number;
  totalRevenue?: number;
  pendingPayments?: number;
  overduePayments?: number;

  // Statistiques des abonnements
  activeSubscriptions?: number;
  expiringSubscriptions?: number;

  // Taux d'occupation
  occupancyRate?: number;
  currentOccupancy?: number;
  totalCapacity?: number;

  // Métadonnées
  siteId?: string;
  siteName?: string;
  calculatedAt?: number; // timestamp
}

@Injectable({
  providedIn: 'root'
})
export class DashboardService {
  private http = inject(HttpClient);
  private apiUrl = `${environment.apiUrl}/api/dashboard`;

  /**
   * Get complete dashboard statistics for a site
   */
  getDashboardStats(siteId: string): Observable<DashboardStats> {
    const params = new HttpParams().set('siteId', siteId);
    const url = `${this.apiUrl}/stats`;

    console.log('🔍 DashboardService: Making request to:', url);
    console.log('🔍 DashboardService: With params:', { siteId });

    return this.http.get<DashboardStats>(url, { params }).pipe(
      catchError(error => {
        console.error('❌ Error fetching dashboard stats:', error);
        console.error('❌ Error details:', {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          message: error.message
        });
        return throwError(() => error);
      })
    );
  }

  /**
   * Get member statistics for a site
   */
  getMemberStats(siteId: string): Observable<DashboardStats> {
    const params = new HttpParams().set('siteId', siteId);

    return this.http.get<DashboardStats>(`${this.apiUrl}/stats/members`, { params }).pipe(
      catchError(error => {
        console.error('Error fetching member stats:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get space statistics for a site
   */
  getSpaceStats(siteId: string): Observable<DashboardStats> {
    const params = new HttpParams().set('siteId', siteId);

    return this.http.get<DashboardStats>(`${this.apiUrl}/stats/spaces`, { params }).pipe(
      catchError(error => {
        console.error('Error fetching space stats:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get reservation statistics for a site
   */
  getReservationStats(siteId: string): Observable<DashboardStats> {
    const params = new HttpParams().set('siteId', siteId);

    return this.http.get<DashboardStats>(`${this.apiUrl}/stats/reservations`, { params }).pipe(
      catchError(error => {
        console.error('Error fetching reservation stats:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get financial statistics for a site
   */
  getFinancialStats(siteId: string): Observable<DashboardStats> {
    const params = new HttpParams().set('siteId', siteId);

    return this.http.get<DashboardStats>(`${this.apiUrl}/stats/financial`, { params }).pipe(
      catchError(error => {
        console.error('Error fetching financial stats:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get subscription statistics for a site
   */
  getSubscriptionStats(siteId: string): Observable<DashboardStats> {
    const params = new HttpParams().set('siteId', siteId);

    return this.http.get<DashboardStats>(`${this.apiUrl}/stats/subscriptions`, { params }).pipe(
      catchError(error => {
        console.error('Error fetching subscription stats:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get occupancy rate for a site
   */
  getOccupancyRate(siteId: string): Observable<number> {
    const params = new HttpParams().set('siteId', siteId);

    return this.http.get<number>(`${this.apiUrl}/stats/occupancy`, { params }).pipe(
      catchError(error => {
        console.error('Error fetching occupancy rate:', error);
        return throwError(() => error);
      })
    );
  }
}
