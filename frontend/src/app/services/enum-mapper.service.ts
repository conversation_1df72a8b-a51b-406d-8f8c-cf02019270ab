import { Injectable } from '@angular/core';
import { SpaceType, SpaceStatus, EquipmentType, EquipmentStatus, ReservationStatus, Floor } from '../models/space.model';

@Injectable({
  providedIn: 'root'
})
export class EnumMapperService {

  constructor() { }

  // Mapping pour SpaceType
  getSpaceTypeDisplayName(type: SpaceType | string | null): string {
    if (!type) return 'Non spécifié';

    const spaceTypeMap: { [key: string]: string } = {
      'WORKSTATION': 'Poste de travail',
      'PRIVATE_OFFICE': 'Bureau privé',
      'MEETING_ROOM': 'Salle de réunion',
      'PHONE_BOOTH': 'Cabine téléphonique',
      'LOUNGE': 'Espace détente',
      'CONFERENCE_ROOM': 'Salle de conférence',
      'HOT_DESK': 'Bureau partagé',
      'DEDICATED_DESK': 'Bureau dédié',
      'COLLABORATIVE': 'Espace collaboratif',
      'EVENT_SPACE': 'Espace événementiel'
    };

    return spaceTypeMap[type.toString()] || type.toString();
  }

  // Mapping pour SpaceStatus
  getSpaceStatusDisplayName(status: SpaceStatus | string | null): string {
    if (!status) return 'Non spécifié';

    const spaceStatusMap: { [key: string]: string } = {
      'AVAILABLE': 'Disponible',
      'OCCUPIED': 'Occupé',
      'MAINTENANCE': 'En maintenance',
      'OUT_OF_ORDER': 'Hors service',
      'RESERVED': 'Réservé'
    };

    return spaceStatusMap[status.toString()] || status.toString();
  }

  // Mapping pour EquipmentType
  getEquipmentTypeDisplayName(type: EquipmentType | string | null): string {
    if (!type) return 'Non spécifié';

    const equipmentTypeMap: { [key: string]: string } = {
      'DESK': 'Bureau',
      'CHAIR': 'Chaise',
      'MONITOR': 'Écran',
      'COMPUTER': 'Ordinateur',
      'PRINTER': 'Imprimante',
      'PROJECTOR': 'Projecteur',
      'TV_SCREEN': 'Écran TV',
      'WHITEBOARD': 'Tableau blanc',
      'PHONE': 'Téléphone',
      'SPEAKERS': 'Haut-parleurs',
      'MICROPHONE': 'Microphone',
      'WEBCAM': 'Webcam',
      'STORAGE': 'Rangement',
      'WIFI': 'WiFi',
      'ETHERNET': 'Ethernet'
    };

    return equipmentTypeMap[type.toString()] || type.toString();
  }

  // Mapping pour EquipmentStatus
  getEquipmentStatusDisplayName(status: EquipmentStatus | string | null): string {
    if (!status) return 'Non spécifié';

    const equipmentStatusMap: { [key: string]: string } = {
      'WORKING': 'Fonctionnel',
      'BROKEN': 'En panne',
      'MAINTENANCE': 'En maintenance'
    };

    return equipmentStatusMap[status.toString()] || status.toString();
  }

  // Mapping pour ReservationStatus
  getReservationStatusDisplayName(status: ReservationStatus | string | null): string {
    if (!status) return 'Non spécifié';

    const reservationStatusMap: { [key: string]: string } = {
      'PENDING': 'En attente',
      'CONFIRMED': 'Confirmée',
      'CANCELLED': 'Annulée',
      'COMPLETED': 'Terminée',
      'NO_SHOW': 'Absence'
    };

    return reservationStatusMap[status.toString()] || status.toString();
  }

  // Méthode utilitaire pour obtenir toutes les options d'un enum avec leurs labels
  getSpaceTypeOptions(): { value: SpaceType, label: string }[] {
    return Object.values(SpaceType).map(type => ({
      value: type,
      label: this.getSpaceTypeDisplayName(type)
    }));
  }

  getSpaceStatusOptions(): { value: SpaceStatus, label: string }[] {
    return Object.values(SpaceStatus).map(status => ({
      value: status,
      label: this.getSpaceStatusDisplayName(status)
    }));
  }

  getEquipmentTypeOptions(): { value: EquipmentType, label: string }[] {
    return Object.values(EquipmentType).map(type => ({
      value: type,
      label: this.getEquipmentTypeDisplayName(type)
    }));
  }

  getEquipmentStatusOptions(): { value: EquipmentStatus, label: string }[] {
    return Object.values(EquipmentStatus).map(status => ({
      value: status,
      label: this.getEquipmentStatusDisplayName(status)
    }));
  }

  // Mapping pour Floor
  getFloorDisplayName(floor: Floor | string | null): string {
    if (!floor) return 'Non spécifié';

    const floorMap: { [key: string]: string } = {
      'BASEMENT': 'Sous-sol',
      'GROUND_FLOOR': 'Rez-de-chaussée',
      'FIRST_FLOOR': '1er étage',
      'SECOND_FLOOR': '2ème étage',
      'THIRD_FLOOR': '3ème étage',
      'FOURTH_FLOOR': '4ème étage',
      'FIFTH_FLOOR': '5ème étage',
      'SIXTH_FLOOR': '6ème étage',
      'SEVENTH_FLOOR': '7ème étage',
      'EIGHTH_FLOOR': '8ème étage',
      'NINTH_FLOOR': '9ème étage',
      'TENTH_FLOOR': '10ème étage'
    };

    return floorMap[floor.toString()] || floor.toString();
  }

  getFloorOptions(): { value: Floor, label: string }[] {
    return Object.values(Floor).map(floor => ({
      value: floor,
      label: this.getFloorDisplayName(floor)
    }));
  }

  // Méthode utilitaire pour formater les valeurs null/undefined
  formatDisplayValue(value: any, defaultText: string = 'Non spécifié'): string {
    if (value === null || value === undefined || value === '') {
      return defaultText;
    }
    return value.toString();
  }
}
