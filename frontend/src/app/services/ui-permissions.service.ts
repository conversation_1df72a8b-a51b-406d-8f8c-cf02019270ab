import { Injectable } from '@angular/core';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class UiPermissionsService {

  constructor(private authService: AuthService) {}

  /**
   * Vérifie si l'utilisateur peut voir la colonne abonnement
   */
  canShowSubscriptionColumn(): boolean {
    return this.authService.canAccessSubscriptions();
  }

  /**
   * Vérifie si l'utilisateur peut voir les informations de facturation
   */
  canShowBillingInfo(): boolean {
    return this.authService.canAccessBilling();
  }

  /**
   * Vérifie si l'utilisateur peut voir les statistiques
   */
  canShowStatistics(): boolean {
    return this.authService.canAccessStatistics();
  }

  /**
   * Vérifie si l'utilisateur peut gérer plusieurs sites
   */
  canShowMultiSiteFeatures(): boolean {
    return this.authService.canAccessMultiSite();
  }

  /**
   * Retourne les colonnes à afficher pour la liste des membres
   */
  getMemberTableColumns(): string[] {
    const baseColumns = [
      'firstName',
      'lastName',
      'email',
      'phone',
      'memberType',
      'status',
      'actions'
    ];

    // Ajouter la colonne abonnement seulement si l'utilisateur a les permissions
    if (this.canShowSubscriptionColumn()) {
      baseColumns.splice(-1, 0, 'subscription'); // Insérer avant 'actions'
    }

    return baseColumns;
  }

  /**
   * Retourne les colonnes à afficher pour la liste des espaces
   */
  getSpaceTableColumns(): string[] {
    const baseColumns = [
      'name',
      'type',
      'capacity',
      'location',
      'floor',
      'status',
      'actions'
    ];

    // Tous les plans peuvent voir les espaces
    return baseColumns;
  }

  /**
   * Retourne les colonnes à afficher pour la liste des réservations
   */
  getReservationTableColumns(): string[] {
    const baseColumns = [
      'spaceName',
      'memberName',
      'startTime',
      'endTime',
      'status',
      'actions'
    ];

    // Tous les plans peuvent voir les réservations
    return baseColumns;
  }

  /**
   * Retourne les onglets à afficher dans le tableau de bord
   */
  getDashboardTabs(): Array<{key: string, label: string, visible: boolean}> {
    return [
      {
        key: 'overview',
        label: 'Vue d\'ensemble',
        visible: true // Tous les plans
      },
      {
        key: 'spaces',
        label: 'Espaces',
        visible: true // Tous les plans
      },
      {
        key: 'members',
        label: 'Membres',
        visible: true // Tous les plans
      },
      {
        key: 'reservations',
        label: 'Réservations',
        visible: true // Tous les plans
      },
      {
        key: 'subscriptions',
        label: 'Abonnements',
        visible: this.canShowSubscriptionColumn()
      },
      {
        key: 'billing',
        label: 'Facturation',
        visible: this.canShowBillingInfo()
      },
      {
        key: 'statistics',
        label: 'Statistiques',
        visible: this.canShowStatistics()
      }
    ];
  }

  /**
   * Retourne les actions disponibles pour un membre selon les permissions
   */
  getMemberActions(): Array<{key: string, label: string, visible: boolean}> {
    return [
      {
        key: 'view',
        label: 'Voir',
        visible: true
      },
      {
        key: 'edit',
        label: 'Modifier',
        visible: true
      },
      {
        key: 'delete',
        label: 'Supprimer',
        visible: true
      },
      {
        key: 'assignSubscription',
        label: 'Assigner un abonnement',
        visible: this.canShowSubscriptionColumn()
      }
    ];
  }

  /**
   * Vérifie si l'utilisateur est sur le plan BASIC
   */
  isBasicPlan(): boolean {
    return this.authService.isBasicPlan();
  }

  /**
   * Vérifie si l'utilisateur est sur un plan avec abonnements
   */
  hasSubscriptionAccess(): boolean {
    return this.authService.canAccessSubscriptions();
  }

  /**
   * Retourne un message d'information pour les fonctionnalités non disponibles
   */
  getUpgradeMessage(feature: string): string {
    const currentPlan = this.getCurrentPlanName();
    const availablePlans = this.getAvailablePlansForFeature(feature);

    return `🚀 La fonctionnalité "${feature}" n'est pas disponible avec votre plan ${currentPlan}.
            Disponible avec : ${availablePlans.join(', ')}.
            ✨ Passez à un plan supérieur pour débloquer cette fonctionnalité et bien plus encore !`;
  }

  /**
   * Retourne le nom du plan actuel
   */
  getCurrentPlanName(): string {
    if (this.authService.isBasicPlan()) return 'BASIC';
    if (this.authService.isStarterPlan()) return 'STARTER';
    if (this.authService.isProfessionalPlan()) return 'PROFESSIONAL';
    if (this.authService.isEnterprisePlan()) return 'ENTREPRISE';
    return 'Non défini';
  }

  /**
   * Retourne les plans disponibles pour une fonctionnalité
   */
  getAvailablePlansForFeature(feature: string): string[] {
    const featureMap: { [key: string]: string[] } = {
      'Abonnements': ['STARTER', 'PROFESSIONAL', 'ENTREPRISE'],
      'Facturation': ['STARTER', 'PROFESSIONAL', 'ENTREPRISE'],
      'Statistiques': ['STARTER', 'PROFESSIONAL', 'ENTREPRISE'],
      'Multi-site': ['PROFESSIONAL', 'ENTREPRISE']
    };

    return featureMap[feature] || ['STARTER', 'PROFESSIONAL', 'ENTREPRISE'];
  }

  /**
   * Retourne un message d'upgrade avec call-to-action
   */
  getUpgradeMessageWithCTA(feature: string): { message: string, cta: string } {
    const currentPlan = this.getCurrentPlanName();
    const availablePlans = this.getAvailablePlansForFeature(feature);
    const nextPlan = this.getNextRecommendedPlan();

    return {
      message: `🚀 Débloquez "${feature}" avec le plan ${nextPlan} !
                Votre plan ${currentPlan} ne donne pas accès à cette fonctionnalité premium.`,
      cta: `Passer au plan ${nextPlan}`
    };
  }

  /**
   * Retourne le prochain plan recommandé
   */
  private getNextRecommendedPlan(): string {
    if (this.isBasicPlan()) return 'STARTER';
    if (this.authService.isStarterPlan()) return 'PROFESSIONAL';
    if (this.authService.isProfessionalPlan()) return 'ENTREPRISE';
    return 'STARTER';
  }
}
