import { Injectable } from '@angular/core';
import { FormGroup, AbstractControl, FormArray } from '@angular/forms';

export interface ValidationMessage {
  type: string;
  message: string;
}

export interface ValidationMessages {
  [key: string]: ValidationMessage[];
}

@Injectable({
  providedIn: 'root'
})
export class ErrorService {

  constructor() { }

  /**
   * Récupère le message d'erreur pour un contrôle donné
   * @param controlName Nom du contrôle
   * @param formGroup Groupe de formulaire
   * @param validationMessages Messages de validation définis pour ce formulaire
   * @returns Message d'erreur ou chaîne vide
   */
  getErrorMessage(
    controlName: string,
    formGroup: FormGroup,
    validationMessages: ValidationMessages
  ): string {
    const control = formGroup.get(controlName);

    if (!control || !control.errors || (!control.dirty && !control.touched)) {
      return '';
    }

    const fieldValidationMessages = validationMessages[controlName];
    if (!fieldValidationMessages) {
      return '';
    }

    // Parcourir les erreurs du contrôle et trouver le message correspondant
    for (const errorType in control.errors) {
      const validationMessage = fieldValidationMessages.find(msg => msg.type === errorType);
      if (validationMessage) {
        return validationMessage.message;
      }
    }

    return '';
  }

  /**
   * Vérifie si un champ est invalide et doit afficher une erreur
   * @param controlName Nom du contrôle
   * @param formGroup Groupe de formulaire
   * @returns true si le champ est invalide et doit afficher une erreur
   */
  isFieldInvalid(controlName: string, formGroup: FormGroup): boolean {
    const control = formGroup.get(controlName);
    return !!(control && control.invalid && (control.dirty || control.touched));
  }

  /**
   * Marque tous les contrôles d'un FormGroup comme touchés
   * @param formGroup Groupe de formulaire
   */
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control) {
        control.markAsTouched();

        // Si c'est un FormGroup imbriqué, appeler récursivement
        if (control instanceof FormGroup) {
          this.markFormGroupTouched(control);
        }
      }
    });
  }

  getFormValidationMessage(formGroup: FormGroup): string {
    // Vérifier s'il y a des champs requis non remplis
    const hasEmptyRequiredFields = this.hasEmptyRequiredFields(formGroup);

    if (hasEmptyRequiredFields) {
      return 'Veuillez remplir tous les champs requis';
    } else {
      return 'Veuillez corriger les erreurs dans le formulaire';
    }
  }

  private hasEmptyRequiredFields(formGroup: FormGroup): boolean {
    for (const key of Object.keys(formGroup.controls)) {
      const control = formGroup.get(key);

      if (control instanceof FormGroup) {
        if (this.hasEmptyRequiredFields(control)) {
          return true;
        }
      } else if (control instanceof FormArray) {
        for (const arrayControl of control.controls) {
          if (arrayControl instanceof FormGroup && this.hasEmptyRequiredFields(arrayControl)) {
            return true;
          }
        }
      } else {
        // Vérifier si le contrôle a une erreur 'required'
        if (control?.errors?.['required']) {
          return true;
        }
      }
    }
    return false;
  }
}
