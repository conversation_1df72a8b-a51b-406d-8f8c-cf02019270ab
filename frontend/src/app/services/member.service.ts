import { Injectable, signal } from '@angular/core';
import { Observable, of, delay, map, catchError, throwError } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '../../environments/environment';
import {
  Member,
  MemberType,
  MemberStatus,
  MemberHistory,
  HistoryType,
  Subscription,
  CreateMemberRequest,
  UpdateMemberRequest
} from '../models/member.model';
import { PageRequest, PageResponse, PageRequestBuilder } from '../models/pagination.model';
import { SiteContextService } from './site-context.service';

@Injectable({
  providedIn: 'root'
})
export class MemberService {
  // URL de l'API backend
  private readonly apiUrl = `${environment.apiUrl}/api/members`;
  private readonly subscriptionsApiUrl = `${environment.apiUrl}/api/subscriptions`;

  // Signaux pour la gestion d'état
  private membersSignal = signal<Member[]>([]);
  private subscriptionsSignal = signal<Subscription[]>([]);
  private loadingSignal = signal<boolean>(false);

  // Getters publics pour les signaux
  members = this.membersSignal.asReadonly();
  subscriptions = this.subscriptionsSignal.asReadonly();
  loading = this.loadingSignal.asReadonly();

  constructor(
    private http: HttpClient,
    private siteContext: SiteContextService
  ) {
    // Les données sont maintenant chargées à la demande depuis le backend
  }

  /**
   * Obtenir l'ID du site actuel depuis le contexte
   */
  private getCurrentSiteId(): string {
    const currentSite = this.siteContext.getCurrentSite();
    if (!currentSite || !currentSite.id) {
      console.warn('⚠️ No site selected in context, using fallback');
      return '1'; // Fallback vers le site ID 1
    }
    return currentSite.id;
  }

  // Récupérer tous les membres depuis le backend (version non paginée - pour compatibilité)
  getMembers(): Observable<Member[]> {
    this.loadingSignal.set(true);

    const params = new HttpParams()
      .set('siteId', this.getCurrentSiteId())
      .set('size', '1000'); // Récupérer un grand nombre pour simuler "tous les membres"

    return this.http.get<any>(this.apiUrl, { params }).pipe(
      map(response => {
        // Le backend renvoie maintenant une réponse paginée
        const memberDataList = response.content || [];
        // Convertir les données backend en membres frontend
        const members = memberDataList.map((memberData: any) => this.mapBackendDataToMember(memberData));
        this.membersSignal.set(members);
        this.loadingSignal.set(false);
        return members;
      }),
      catchError(error => {
        console.error('Error fetching members from backend:', error);
        this.loadingSignal.set(false);
        // Fallback vers les données mockées en cas d'erreur
        return of(this.membersSignal());
      })
    );
  }

  // Récupérer les membres avec pagination
  getMembersPaginated(pageRequest: PageRequest, status?: string, searchTerm?: string, memberType?: string, subscriptionId?: number): Observable<PageResponse<Member>> {
    this.loadingSignal.set(true);

    let params = new HttpParams()
      .set('siteId', this.getCurrentSiteId())
      .set('page', pageRequest.page.toString())
      .set('size', pageRequest.size.toString())
      .set('sortDirection', pageRequest.sortDirection || 'ASC');

    if (pageRequest.sortBy) {
      params = params.set('sortBy', pageRequest.sortBy);
    }

    if (status && status !== 'all') {
      params = params.set('status', status.toUpperCase());
    }

    if (searchTerm && searchTerm.trim()) {
      params = params.set('search', searchTerm.trim());
    }

    if (memberType && memberType !== 'all') {
      params = params.set('memberType', memberType);
    }

    console.log('subscriptionId value before check:', subscriptionId, typeof subscriptionId); // Debug

    if (subscriptionId !== undefined && subscriptionId !== null) {
      params = params.set('subscriptionId', subscriptionId.toString());
      console.log('subscriptionId added to params:', subscriptionId); // Debug
    } else {
      console.log('subscriptionId not added (undefined or null)'); // Debug
    }

    console.log('Sending request to /api/members with params:', params.toString()); // Debug

    return this.http.get<any>(this.apiUrl, { params }).pipe(
      map((response: any) => {
        console.log('Raw backend response for members:', response); // Debug
        console.log('Response type:', typeof response, 'Is array:', Array.isArray(response)); // Debug

        // Si la réponse est un tableau (ancienne API), on la convertit en PageResponse
        if (Array.isArray(response)) {
          console.warn('Backend returned array instead of PageResponse for members, converting...'); // Debug
          const pageResponse: PageResponse<any> = {
            content: response,
            page: pageRequest.page,
            size: pageRequest.size,
            totalElements: response.length,
            totalPages: Math.ceil(response.length / pageRequest.size),
            first: pageRequest.page === 0,
            last: pageRequest.page >= Math.ceil(response.length / pageRequest.size) - 1,
            empty: response.length === 0,
            numberOfElements: response.length
          };
          return pageResponse;
        }

        // Si c'est déjà un objet PageResponse
        if (response && response.content && Array.isArray(response.content)) {
          return response as PageResponse<any>;
        }

        // Structure invalide
        console.error('Invalid response structure for members:', response);
        throw new Error('Invalid response structure from backend');
      }),
      map((pageResponse: PageResponse<any>) => {
        // Convertir les données backend en membres frontend
        const members = pageResponse.content.map((memberData: any) => this.mapBackendDataToMember(memberData));

        // Mettre à jour le signal local avec les membres de la page courante
        this.membersSignal.set(members);
        this.loadingSignal.set(false);

        // Retourner la réponse paginée avec les membres convertis
        return {
          ...pageResponse,
          content: members
        };
      }),
      catchError(error => {
        console.error('Error fetching paginated members from backend:', error);
        this.loadingSignal.set(false);
        // Retourner une page vide en cas d'erreur
        return of({
          content: [],
          page: pageRequest.page,
          size: pageRequest.size,
          totalElements: 0,
          totalPages: 0,
          first: true,
          last: true,
          empty: true,
          numberOfElements: 0
        });
      })
    );
  }

  // Récupérer un membre par ID depuis le backend
  getMemberById(id: string): Observable<Member | undefined> {
    const params = new HttpParams().set('siteId', this.getCurrentSiteId());

    return this.http.get<any>(`${this.apiUrl}/${id}`, { params }).pipe(
      map(memberData => memberData ? this.mapBackendDataToMember(memberData) : undefined),
      catchError(error => {
        console.error('Error fetching member by ID from backend:', error);
        // Fallback vers les données mockées
        return of(this.membersSignal().find(member => member.id === id));
      })
    );
  }

  // Créer un nouveau membre
  createMember(request: CreateMemberRequest): Observable<Member> {
    const params = new HttpParams().set('siteId', this.getCurrentSiteId());

    // Mapper la requête frontend vers le format backend
    const backendRequest = {
      firstName: request.firstName,
      lastName: request.lastName,
      email: request.email,
      phone: request.phone,
      company: request.company || '',
      memberType: request.memberType,
      subscriptionId: request.subscriptionId ? parseInt(request.subscriptionId) : null,
      studentCode: request.studentCode,
      iceNumber: request.iceNumber,
      isActive: true
    };

    return this.http.post<any>(this.apiUrl, backendRequest, { params }).pipe(
      map(memberData => {
        const newMember = this.mapBackendDataToMember(memberData);
        // Ajouter le nouveau membre à la liste
        const currentMembers = this.membersSignal();
        this.membersSignal.set([...currentMembers, newMember]);
        return newMember;
      }),
      catchError(error => {
        console.error('Error creating member:', error);
        // Propager l'erreur au lieu de créer un fallback
        return throwError(() => error);
      })
    );
  }

  // Mettre à jour un membre
  updateMember(id: string, updateData: UpdateMemberRequest): Observable<Member> {
    this.loadingSignal.set(true);

    // Mapper la requête frontend vers le format backend
    const backendRequest = {
      firstName: updateData.firstName,
      lastName: updateData.lastName,
      email: updateData.email,
      phone: updateData.phone,
      company: updateData.company || '',
      memberType: updateData.memberType,
      subscriptionId: updateData.subscriptionId ? parseInt(updateData.subscriptionId) : null,
      studentCode: updateData.studentCode,
      iceNumber: updateData.iceNumber,
      isActive: updateData.status === MemberStatus.ACTIVE
    };

    return this.http.put<any>(`${this.apiUrl}/${id}`, backendRequest).pipe(
      map(memberData => {
        const updatedMember = this.mapBackendDataToMember(memberData);
        // Mettre à jour la liste locale
        const currentMembers = this.membersSignal();
        const memberIndex = currentMembers.findIndex(m => m.id === id);

        if (memberIndex !== -1) {
          const newMembers = [...currentMembers];
          newMembers[memberIndex] = updatedMember;
          this.membersSignal.set(newMembers);
        }

        this.loadingSignal.set(false);
        return updatedMember;
      }),
      catchError(error => {
        console.error('Error updating member:', error);
        this.loadingSignal.set(false);
        // Propager l'erreur au lieu de créer un fallback
        return throwError(() => error);
      })
    );
  }

  // Supprimer un membre
  deleteMember(id: string): Observable<boolean> {
    this.loadingSignal.set(true);

    return of(true).pipe(
      delay(500),
      map(() => {
        const currentMembers = this.membersSignal();
        const filteredMembers = currentMembers.filter(m => m.id !== id);
        this.membersSignal.set(filteredMembers);
        this.loadingSignal.set(false);
        return true;
      })
    );
  }

  /**
   * Get members count for a site
   */
  getMembersCount(siteId: number): Observable<number> {
    const params = new HttpParams().set('siteId', siteId.toString());

    return this.http.get<number>(`${this.apiUrl}/count`, { params }).pipe(
      catchError(error => {
        console.error('Error fetching members count:', error);
        return throwError(() => error);
      })
    );
  }



  // Récupérer les abonnements par type de membre depuis le backend
  getSubscriptionsByType(memberType: MemberType): Observable<Subscription[]> {
    const params = new HttpParams()
      .set('siteId', this.getCurrentSiteId())
      .set('memberType', memberType)
      .set('active', 'true');

    return this.http.get<any[]>(`${this.subscriptionsApiUrl}/compatible`, { params }).pipe(
      map(subscriptionDataList => {
        // Convertir les données backend en abonnements frontend
        return subscriptionDataList.map(subscriptionData => this.mapBackendDataToSubscription(subscriptionData));
      }),
      catchError(error => {
        console.error('Error fetching subscriptions from backend:', error);
        // En cas d'erreur, retourner un tableau vide
        return of([]);
      })
    );
  }

  // Charger tous les abonnements actifs depuis le backend
  loadAllSubscriptions(): Observable<Subscription[]> {
    const params = new HttpParams()
      .set('siteId', this.getCurrentSiteId())
      .set('active', 'true');

    return this.http.get<any[]>(this.subscriptionsApiUrl, { params }).pipe(
      map(subscriptionDataList => {
        // Convertir les données backend en abonnements frontend
        const subscriptions = subscriptionDataList.map(subscriptionData => this.mapBackendDataToSubscription(subscriptionData));
        // Mettre à jour le signal local
        this.subscriptionsSignal.set(subscriptions);
        return subscriptions;
      }),
      catchError(error => {
        console.error('Error loading all subscriptions from backend:', error);
        // En cas d'erreur, retourner un tableau vide
        this.subscriptionsSignal.set([]);
        return of([]);
      })
    );
  }

  // Récupérer l'historique d'un membre
  getMemberHistory(memberId: string): Observable<MemberHistory[]> {
    // Mock data pour l'historique
    const mockHistory: MemberHistory[] = [
      {
        id: '1',
        memberId,
        type: HistoryType.PAYMENT,
        description: 'Paiement abonnement mensuel',
        amount: 499,
        date: new Date('2024-07-01'),
        details: { method: 'carte', reference: 'PAY-2024-001' }
      },
      {
        id: '2',
        memberId,
        type: HistoryType.RESERVATION,
        description: 'Réservation salle de réunion A',
        date: new Date('2024-07-15'),
        details: { room: 'Salle A', duration: '2h', time: '14:00-16:00' }
      }
    ];

    return of(mockHistory).pipe(delay(300));
  }

  // Méthode de mapping pour convertir les données backend en membre frontend
  private mapBackendDataToMember(memberData: any): Member {
    return {
      id: memberData.memberId?.toString() || memberData.id?.toString() || '', // Support des deux formats d'ID
      firstName: memberData.firstName,
      lastName: memberData.lastName,
      email: memberData.email,
      phone: memberData.phone || '',
      company: memberData.company || '',
      memberType: memberData.memberType || this.inferMemberType(memberData),
      subscriptionType: memberData.subscriptionName || '-', // Utiliser directement le nom du backend
      status: memberData.isActive ? MemberStatus.ACTIVE : MemberStatus.INACTIVE,
      studentCode: memberData.studentCode || this.generateStudentCode(memberData),
      iceNumber: memberData.iceNumber || this.generateIceNumber(memberData),
      createdAt: memberData.createdAt ? new Date(memberData.createdAt) : new Date(),
      updatedAt: memberData.updatedAt ? new Date(memberData.updatedAt) : (memberData.createdAt ? new Date(memberData.createdAt) : new Date())
    };
  }

  // Inférer le type de membre basé sur les données
  private inferMemberType(memberData: any): MemberType {
    // Si les données ont déjà un type défini, l'utiliser
    if (memberData.memberType) {
      return memberData.memberType;
    }

    // Logique d'inférence pour les anciennes données
    if (memberData.email && (memberData.email.includes('etu') || memberData.email.includes('student'))) {
      return MemberType.STUDENT;
    }
    if (memberData.company && memberData.company.trim() !== '') {
      return MemberType.COMPANY;
    }
    return MemberType.PROFESSIONAL;
  }



  // Mapper les données backend vers un abonnement frontend
  private mapBackendDataToSubscription(subscriptionData: any): Subscription {
    return {
      id: subscriptionData.subscriptionId.toString(),
      name: subscriptionData.name,
      description: subscriptionData.description || '',
      price: subscriptionData.price,
      currency: subscriptionData.currency || 'MAD',
      duration: subscriptionData.durationDays,
      memberType: subscriptionData.memberType,
      features: subscriptionData.features ? subscriptionData.features.split(',') : [],
      maxReservationsPerDay: subscriptionData.maxReservationsPerDay,
      maxReservationsPerWeek: subscriptionData.maxReservationsPerWeek,
      maxReservationsPerMonth: subscriptionData.maxReservationsPerMonth,
      maxConsecutiveHours: subscriptionData.maxConsecutiveHours,
      advanceBookingDays: subscriptionData.advanceBookingDays,
      canBookMeetingRooms: subscriptionData.canBookMeetingRooms,
      canAccessPremiumAreas: subscriptionData.canAccessPremiumAreas,
      isActive: subscriptionData.isActive
    };
  }



  // Générer un code étudiant si c'est un étudiant
  private generateStudentCode(memberData: any): string | undefined {
    const memberType = memberData.memberType || this.inferMemberType(memberData);
    if (memberType === MemberType.STUDENT && !memberData.studentCode) {
      const memberId = memberData.memberId || memberData.id || '001';
      return `ETU${new Date().getFullYear()}${memberId.toString().padStart(3, '0')}`;
    }
    return undefined;
  }

  // Générer un numéro ICE si c'est une entreprise
  private generateIceNumber(memberData: any): string | undefined {
    const memberType = memberData.memberType || this.inferMemberType(memberData);
    if (memberType === MemberType.COMPANY && !memberData.iceNumber) {
      const memberId = memberData.memberId || memberData.id || '001';
      return `ICE${memberId.toString().padStart(9, '0')}`;
    }
    return undefined;
  }
}
