import { Injectable } from '@angular/core';
import jsPDF from 'jspdf';
import { PaymentHistory } from '../models/invoice.model';

@Injectable({
  providedIn: 'root'
})
export class PdfService {

  constructor() { }

  // Générer un reçu de paiement PDF
  generatePaymentReceipt(
    payment: PaymentHistory,
    memberInfo: any,
    recentPayments: PaymentHistory[]
  ): void {
    const doc = new jsPDF();

    // Configuration
    const pageWidth = doc.internal.pageSize.width;
    const margin = 20;
    let yPosition = 30;

    // En-tête
    doc.setFontSize(20);
    doc.setFont('helvetica', 'bold');
    doc.text('WORKEEM', margin, yPosition);

    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text('Espace de coworking moderne', margin, yPosition + 8);
    doc.text('Technopolis, Hay Riad, Rabat, Mar<PERSON>', margin, yPosition + 16);
    doc.text('<EMAIL> | +212 5 37 12 34 56', margin, yPosition + 24);

    // Ligne de séparation
    yPosition += 40;
    doc.line(margin, yPosition, pageWidth - margin, yPosition);

    // Titre du reçu
    yPosition += 20;
    doc.setFontSize(18);
    doc.setFont('helvetica', 'bold');
    doc.text('REÇU DE PAIEMENT', margin, yPosition);

    // Informations du paiement principal
    yPosition += 20;
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Détails du paiement', margin, yPosition);

    yPosition += 15;
    doc.setFont('helvetica', 'normal');
    doc.text(`Référence: ${payment.reference}`, margin, yPosition);
    doc.text(`Date: ${payment.date.toLocaleDateString('fr-FR')}`, margin, yPosition + 8);
    doc.text(`Montant: ${payment.amount.toFixed(2)} MAD`, margin, yPosition + 16);
    doc.text(`Méthode: ${this.getPaymentMethodLabel(payment.method)}`, margin, yPosition + 24);

    if (payment.notes) {
      doc.text(`Notes: ${payment.notes}`, margin, yPosition + 32);
      yPosition += 8;
    }

    // Informations du member
    yPosition += 50;
    doc.setFont('helvetica', 'bold');
    doc.text('Informations member', margin, yPosition);

    yPosition += 15;
    doc.setFont('helvetica', 'normal');
    doc.text(`Nom: ${memberInfo.firstName} ${memberInfo.lastName}`, margin, yPosition);
    doc.text(`Email: ${memberInfo.email}`, margin, yPosition + 8);
    if (memberInfo.company) {
      doc.text(`Entreprise: ${memberInfo.company}`, margin, yPosition + 16);
      yPosition += 8;
    }
    if (memberInfo.phone) {
      doc.text(`Téléphone: ${memberInfo.phone}`, margin, yPosition + 16);
      yPosition += 8;
    }

    // Historique des 3 derniers paiements
    yPosition += 30;
    doc.setFont('helvetica', 'bold');
    doc.text('Historique des paiements récents', margin, yPosition);

    yPosition += 15;
    doc.setFont('helvetica', 'normal');

    // En-têtes du tableau
    const tableHeaders = ['Date', 'Référence', 'Montant', 'Méthode'];
    const colWidths = [40, 60, 30, 40];
    let xPosition = margin;

    doc.setFont('helvetica', 'bold');
    tableHeaders.forEach((header, index) => {
      doc.text(header, xPosition, yPosition);
      xPosition += colWidths[index];
    });

    // Ligne sous les en-têtes
    yPosition += 5;
    doc.line(margin, yPosition, pageWidth - margin, yPosition);

    // Données du tableau
    yPosition += 10;
    doc.setFont('helvetica', 'normal');

    recentPayments.slice(0, 3).forEach((p) => {
      xPosition = margin;
      const rowData = [
        p.date.toLocaleDateString('fr-FR'),
        p.reference || 'N/A',
        `${p.amount.toFixed(2)} MAD`,
        this.getPaymentMethodLabel(p.method)
      ];

      rowData.forEach((data, index) => {
        doc.text(data, xPosition, yPosition);
        xPosition += colWidths[index];
      });
      yPosition += 12;
    });

    // Pied de page
    yPosition = doc.internal.pageSize.height - 40;
    doc.line(margin, yPosition, pageWidth - margin, yPosition);

    yPosition += 15;
    doc.setFontSize(10);
    doc.setFont('helvetica', 'italic');
    doc.text('Merci de votre confiance !', margin, yPosition);
    doc.text(`Document généré le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}`,
             pageWidth - margin - 80, yPosition);

    // Télécharger le PDF
    doc.save(`recu-paiement-${payment.reference}.pdf`);
  }

  private getPaymentMethodLabel(method: string): string {
    switch (method) {
      case 'card': return 'Carte bancaire';
      case 'transfer': return 'Virement';
      case 'cash': return 'Espèces';
      case 'check': return 'Chèque';
      default: return method;
    }
  }
}
