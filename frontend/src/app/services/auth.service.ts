import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import Keycloak from 'keycloak-js';
import { keycloakConfig, keycloakInitOptions } from '../config/keycloak.config';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private keycloak: Keycloak | undefined;

  constructor(private router: Router) {}

  async init(): Promise<boolean> {
    try {
      this.keycloak = new Keycloak(keycloakConfig);
      const authenticated = await this.keycloak.init(keycloakInitOptions);

      if (authenticated) {
        console.log('User authenticated successfully');
        console.log('Token:', this.keycloak.token);

        // Vérifier s'il y a une URL de redirection stockée
        this.handlePostLoginRedirect();
      }

      return authenticated;
    } catch (error) {
      console.error('Keycloak initialization failed:', error);
      return false;
    }
  }

  getKeycloak(): Keycloak | undefined {
    return this.keycloak;
  }

  isAuthenticated(): boolean {
    return this.keycloak?.authenticated ?? false;
  }

  getToken(): string | undefined {
    return this.keycloak?.token;
  }

  getUsername(): string | undefined {
    return this.keycloak?.tokenParsed?.['preferred_username'];
  }

  // Récupérer le nom de l'organisation depuis le JWT
  getOrganizationName(): string | undefined {
    const tokenParsed = this.keycloak?.tokenParsed;
    if (!tokenParsed) return undefined;

    // Vérifier le claim 'organization' qui est un objet
    const organization = tokenParsed['organization'];
    if (organization && typeof organization === 'object') {
      // Extraire la première clé de l'objet (le nom de l'organisation)
      const organizationKeys = Object.keys(organization);
      if (organizationKeys.length > 0) {
        return organizationKeys[0]; // Retourner "hellodesk" par exemple
      }
    }

    // Fallback vers d'autres claims possibles (strings)
    return tokenParsed['organization_name'] ||
           tokenParsed['org_name'] ||
           tokenParsed['company_name'] ||
           tokenParsed['tenant_name'] ||
           tokenParsed['company'];
  }

  getUserRoles(): string[] {
    const realmAccess = this.keycloak?.tokenParsed?.['realm_access'];
    return realmAccess?.['roles'] || [];
  }

  getResourceRoles(): string[] {
    const resourceAccess = this.keycloak?.tokenParsed?.['resource_access'];
    const workeemApiRoles = resourceAccess?.['workeem-api'];
    return workeemApiRoles?.['roles'] || [];
  }

  hasRole(role: string): boolean {
    return this.getUserRoles().includes(role);
  }

  hasResourceRole(role: string): boolean {
    return this.getResourceRoles().includes(role);
  }

  hasAnyResourceRole(roles: string[]): boolean {
    const userRoles = this.getResourceRoles();
    return roles.some(role => userRoles.includes(role));
  }

  // Méthodes spécifiques pour les plans
  isBasicPlan(): boolean {
    return this.hasResourceRole('BASIC_PLAN');
  }

  isStarterPlan(): boolean {
    return this.hasResourceRole('STARTER_PLAN');
  }

  isProfessionalPlan(): boolean {
    return this.hasResourceRole('PROFESSIONAL_PLAN');
  }

  isEnterprisePlan(): boolean {
    return this.hasResourceRole('ENTREPRISE_PLAN');
  }

  // Vérifier si l'utilisateur a accès aux abonnements
  canAccessSubscriptions(): boolean {
    return this.hasAnyResourceRole(['STARTER_PLAN', 'PROFESSIONAL_PLAN', 'ENTREPRISE_PLAN']);
  }

  // Vérifier si l'utilisateur a accès à la facturation
  canAccessBilling(): boolean {
    return this.hasAnyResourceRole(['STARTER_PLAN', 'PROFESSIONAL_PLAN', 'ENTREPRISE_PLAN']);
  }

  // Vérifier si l'utilisateur a accès aux statistiques
  canAccessStatistics(): boolean {
    return this.hasAnyResourceRole(['STARTER_PLAN', 'PROFESSIONAL_PLAN', 'ENTREPRISE_PLAN']);
  }

  // Vérifier si l'utilisateur a accès au multi-site
  canAccessMultiSite(): boolean {
    return this.hasAnyResourceRole(['PROFESSIONAL_PLAN', 'ENTREPRISE_PLAN']);
  }

  login(): void {
    this.keycloak?.login();
  }

  logout(): void {
    // Effacer le tenant ID stocké
    localStorage.removeItem('workeem_tenant_id');
    this.keycloak?.logout({
      redirectUri: window.location.origin
    });
  }

  refreshToken(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (this.keycloak) {
        this.keycloak.updateToken(30)
          .then(refreshed => {
            if (refreshed) {
              console.log('Token refreshed');
            }
            resolve(refreshed);
          })
          .catch(error => {
            console.error('Failed to refresh token:', error);
            reject(error);
          });
      } else {
        reject('Keycloak not initialized');
      }
    });
  }

  openAccountManagement(): void {
    if (this.keycloak) {
      this.keycloak.accountManagement();
    } else {
      console.error('Keycloak not initialized');
    }
  }

  private handlePostLoginRedirect(): void {
    const redirectUrl = localStorage.getItem('workeem_redirect_url');
    const tenantId = localStorage.getItem('workeem_tenant_id');

    if (redirectUrl && tenantId) {
      console.log('🔄 Redirecting to stored URL after login:', redirectUrl);
      localStorage.removeItem('workeem_redirect_url');
      this.router.navigateByUrl(redirectUrl);
    }
  }
}
