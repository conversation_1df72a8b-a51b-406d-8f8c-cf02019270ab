import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { SiteContextService } from './site-context.service';
import { environment } from '../../environments/environment';

export interface Member {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  company?: string;
  isActive: boolean;
  fullName?: string;
}

@Injectable({
  providedIn: 'root'
})
export class MemberService {
  private readonly apiUrl = `${environment.apiUrl}/api/members`;

  constructor(
    private http: HttpClient,
    private siteContext: SiteContextService
  ) {}

  /**
   * Get current site ID from context
   */
  private getCurrentSiteId(): string {
    const currentSite = this.siteContext.getCurrentSite();
    if (!currentSite || !currentSite.id) {
      console.warn('⚠️ No site selected in context, using fallback');
      return '1'; // Fallback to site ID 1
    }
    return currentSite.id;
  }

  /**
   * Get all members for the current site
   */
  getMembers(): Observable<Member[]> {
    const params = new HttpParams().set('siteId', this.getCurrentSiteId());

    return this.http.get<any[]>(`${this.apiUrl}`, { params }).pipe(
      map(response => this.mapMembersFromBackend(response)),
      catchError(error => {
        console.error('Error fetching members:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get active members only
   */
  getActiveMembers(): Observable<Member[]> {
    const params = new HttpParams()
      .set('siteId', this.getCurrentSiteId())
      .set('active', 'true');

    return this.http.get<any[]>(`${this.apiUrl}`, { params }).pipe(
      map(response => this.mapMembersFromBackend(response)),
      catchError(error => {
        console.error('Error fetching active members:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get member by ID
   */
  getMemberById(id: string): Observable<Member | null> {
    return this.http.get<any>(`${this.apiUrl}/${id}`).pipe(
      map(response => this.mapMemberFromBackend(response)),
      catchError(error => {
        console.error('Error fetching member by id:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Create a new member
   */
  createMember(member: Partial<Member>): Observable<Member> {
    const params = new HttpParams().set('siteId', this.getCurrentSiteId());
    const backendData = this.mapCreateRequestToBackend(member);

    return this.http.post<any>(`${this.apiUrl}`, backendData, { params }).pipe(
      map(response => this.mapMemberFromBackend(response)),
      catchError(error => {
        console.error('Error creating member:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Update an existing member
   */
  updateMember(id: string, member: Partial<Member>): Observable<Member> {
    const backendData = this.mapUpdateRequestToBackend(member);

    return this.http.put<any>(`${this.apiUrl}/${id}`, backendData).pipe(
      map(response => this.mapMemberFromBackend(response)),
      catchError(error => {
        console.error('Error updating member:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Delete a member
   */
  deleteMember(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`).pipe(
      catchError(error => {
        console.error('Error deleting member:', error);
        return throwError(() => error);
      })
    );
  }

  // Méthodes de mapping entre frontend et backend
  private mapMembersFromBackend(backendMembers: any[]): Member[] {
    return backendMembers.map(member => this.mapMemberFromBackend(member));
  }

  private mapMemberFromBackend(backendMember: any): Member {
    return {
      id: backendMember.memberId?.toString() || '',
      firstName: backendMember.firstName || '',
      lastName: backendMember.lastName || '',
      email: backendMember.email || '',
      phone: backendMember.phone || '',
      company: backendMember.company || '',
      isActive: backendMember.isActive !== false,
      fullName: `${backendMember.firstName || ''} ${backendMember.lastName || ''}`.trim()
    };
  }

  private mapCreateRequestToBackend(member: Partial<Member>): any {
    return {
      firstName: member.firstName,
      lastName: member.lastName,
      email: member.email,
      phone: member.phone,
      company: member.company,
      isActive: member.isActive !== false
    };
  }

  private mapUpdateRequestToBackend(member: Partial<Member>): any {
    return this.mapCreateRequestToBackend(member);
  }
}
