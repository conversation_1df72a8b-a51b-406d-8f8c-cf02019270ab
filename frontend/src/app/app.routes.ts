import { Routes } from '@angular/router';
import { AuthGuard } from './guards/auth.guard';
import { NoAuthGuard } from './guards/no-auth.guard';
import { PermissionsGuard } from './guards/permissions.guard';

export const routes: Routes = [
  // Sélection de tenant (accessible seulement si non connecté)
  {
    path: 'tenant-selection',
    loadComponent: () => import('./pages/tenant-selection/tenant-selection.component').then(m => m.TenantSelectionComponent),
    canActivate: [NoAuthGuard]
  },

  // Page non autorisé (accessible sans authentification)
  {
    path: 'unauthorized',
    loadComponent: () => import('./pages/unauthorized/unauthorized.component').then(m => m.UnauthorizedComponent)
  },

  // Redirection par défaut vers welcome (protégée par Keycloak)
  {
    path: '',
    pathMatch: 'full',
    redirectTo: '/welcome'
  },

  // Routes protégées par le guard d'authentification
  {
    path: 'welcome',
    loadChildren: () => import('./pages/welcome/welcome.routes').then(m => m.WELCOME_ROUTES),
    canActivate: [AuthGuard]
  },
  {
    path: 'members',
    loadChildren: () => import('./pages/members/members.routes').then(m => m.MEMBERS_ROUTES),
    canActivate: [AuthGuard]
  },
  {
    path: 'subscriptions',
    loadChildren: () => import('./pages/subscriptions/subscriptions.routes').then(m => m.SUBSCRIPTIONS_ROUTES),
    canActivate: [AuthGuard, PermissionsGuard],
    data: { permissions: ['CAN_MANAGE_SUBSCRIPTIONS'] }
  },
  {
    path: 'spaces',
    loadChildren: () => import('./pages/spaces/spaces.routes').then(m => m.SPACES_ROUTES),
    canActivate: [AuthGuard]
  },
  {
    path: 'reservations',
    loadComponent: () => import('./pages/reservations/reservations.component').then(m => m.ReservationsComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'statistics',
    loadComponent: () => import('./pages/statistics/statistics.component').then(m => m.StatisticsComponent),
    canActivate: [AuthGuard, PermissionsGuard],
    data: { permissions: ['CAN_ACCESS_STATISTICS'] }
  },
  {
    path: 'reservation-form',
    loadComponent: () => import('./pages/reservation-form/reservation-form.component').then(m => m.ReservationFormComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'reservations/:id',
    loadComponent: () => import('./pages/reservation-details/reservation-details.component').then(m => m.ReservationDetailsComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'billing',
    loadComponent: () => import('./pages/billing/billing.component').then(m => m.BillingComponent),
    canActivate: [AuthGuard, PermissionsGuard],
    data: { permissions: ['CAN_ACCESS_BILLING'] }
  },
  {
    path: 'billing/new',
    loadComponent: () => import('./pages/billing-form/billing-form.component').then(m => m.BillingFormComponent),
    canActivate: [AuthGuard, PermissionsGuard],
    data: { permissions: ['CAN_ACCESS_BILLING'] }
  },
  {
    path: 'billing/edit/:id',
    loadComponent: () => import('./pages/billing-form/billing-form.component').then(m => m.BillingFormComponent),
    canActivate: [AuthGuard, PermissionsGuard],
    data: { permissions: ['CAN_ACCESS_BILLING'] }
  },
  {
    path: 'billing/:id',
    loadComponent: () => import('./pages/billing-details/billing-details.component').then(m => m.BillingDetailsComponent),
    canActivate: [AuthGuard, PermissionsGuard],
    data: { permissions: ['CAN_ACCESS_BILLING'] }
  },

  // Route wildcard pour les pages non trouvées
  {
    path: '**',
    redirectTo: '/welcome'
  }
];
