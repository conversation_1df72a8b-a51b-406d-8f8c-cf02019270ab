import { AuthService } from '../services/auth.service';
import { PermissionsService } from '../services/permissions.service';

export function initializeKeycloak(authService: AuthService): () => Promise<boolean> {
  return () => authService.init();
}

export function initializePermissions(permissionsService: PermissionsService): () => void {
  return () => permissionsService.initializePermissions();
}
