import { inject } from '@angular/core';
import { HttpInterceptorFn, HttpErrorResponse } from '@angular/common/http';
import { catchError, throwError } from 'rxjs';
import { AuthService } from '../services/auth.service';
import { ErrorHandlerService } from '../services/error-handler.service';

export const authInterceptor: HttpInterceptorFn = (req, next) => {
  const authService = inject(AuthService);
  const errorHandler = inject(ErrorHandlerService);

  console.log('🔍 Interceptor called for URL:', req.url);
  let modifiedReq = req;

  // Fonctions utilitaires
  const shouldAddToken = (url: string): boolean => {
    return url.includes('/api/') && !isPublicEndpoint(url);
  };

  const isPublicEndpoint = (url: string): boolean => {
    const publicEndpoints = ['/api/tenants/verify'];
    return publicEndpoints.some(endpoint => url.includes(endpoint));
  };

  const shouldAddTenantHeader = (url: string): boolean => {
    return url.includes('/api/') && !url.includes('/api/tenants');
  };

  // Ajouter le token d'authentification si disponible
  const token = authService.getToken();
  const shouldAddTokenFlag = shouldAddToken(req.url);
  const isPublic = isPublicEndpoint(req.url);

  console.log('🔑 Token check:', {
    hasToken: !!token,
    shouldAddToken: shouldAddTokenFlag,
    isPublic,
    url: req.url
  });

  if (token && shouldAddTokenFlag) {
    console.log('✅ Adding Authorization header to request:', req.url);
    modifiedReq = modifiedReq.clone({
      headers: modifiedReq.headers.set('Authorization', `Bearer ${token}`)
    });
  } else {
    console.log('❌ NOT adding Authorization header:', { hasToken: !!token, shouldAddToken: shouldAddTokenFlag });
  }

  // Ajouter le tenant ID si disponible et si c'est un appel vers l'API business
  const tenantId = localStorage.getItem('workeem_tenant_id');
  const shouldAddTenantHeaderFlag = shouldAddTenantHeader(req.url);

  console.log('🏢 Tenant check:', {
    hasTenantId: !!tenantId,
    tenantId,
    shouldAddTenantHeader: shouldAddTenantHeaderFlag,
    url: req.url
  });

  if (tenantId && shouldAddTenantHeaderFlag) {
    console.log('✅ Adding X-Tenant-ID header:', tenantId, 'to request:', req.url);
    modifiedReq = modifiedReq.clone({
      headers: modifiedReq.headers.set('X-Tenant-ID', tenantId)
    });
  } else {
    console.log('❌ NOT adding X-Tenant-ID header:', { hasTenantId: !!tenantId, shouldAddTenantHeader: shouldAddTenantHeaderFlag });
  }

  return next(modifiedReq).pipe(
    catchError((error: HttpErrorResponse) => {
      // Ne gérer les erreurs d'autorisation que si l'utilisateur est censé être authentifié
      const isAuthenticated = authService.isAuthenticated();
      const isAuthError = error.status === 401 || error.status === 403;

      console.log('🚫 HTTP Error:', {
        status: error.status,
        url: req.url,
        isAuthenticated,
        isAuthError,
        isPublic: isPublicEndpoint(req.url)
      });

      // Seulement rediriger vers unauthorized si :
      // 1. C'est une erreur d'autorisation (401/403)
      // 2. L'utilisateur est authentifié (donc c'est un vrai problème de permissions)
      // 3. Ce n'est pas un endpoint public
      if (isAuthError && isAuthenticated && !isPublicEndpoint(req.url)) {
        console.log('🔄 Redirecting to unauthorized page - authenticated user with auth error');
        errorHandler.handleHttpError(error.status, req.url, error.message);
      } else if (isAuthError && !isAuthenticated) {
        console.log('⚠️ Auth error for unauthenticated user - letting guards handle it');
        // Ne pas rediriger, laisser les guards gérer la redirection
      } else if (!isPublicEndpoint(req.url) && error.status !== 400) {
        // Autres erreurs (404, 500, etc.) mais pas 400 qui doit être gérée par les composants
        errorHandler.handleHttpError(error.status, req.url, error.message);
      }

      return throwError(() => error);
    })
  );
};

