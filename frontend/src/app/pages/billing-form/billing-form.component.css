/* Page container */
.billing-form-container {
  padding: 0;
  max-width: 1200px;
  margin: 0 auto;
  min-height: 100vh;
  background: transparent;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px 0;
  background: transparent;
  border-bottom: none;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title nz-icon {
  color: #6E56CF;
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  color: #8E8E93;
  margin: 0;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6E56CF;
  border: 1px solid rgba(110, 86, 207, 0.2);
  border-radius: 8px;
}

.back-button:hover {
  background-color: rgba(110, 86, 207, 0.05);
  border-color: rgba(110, 86, 207, 0.3);
}

/* Form layout */
.form-grid {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 32px;
}

.form-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-item {
  margin-bottom: 0;
}

.form-item.full-width {
  grid-column: 1 / -1;
}

/* Member option styling */
.member-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.member-name {
  font-weight: 500;
  color: #1C1C1E;
}

.member-email {
  font-size: 12px;
  color: #8E8E93;
}

/* Items section */
.items-section {
  margin-bottom: 24px;
}

.item-row {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.item-form {
  display: grid;
  grid-template-columns: 2fr 1fr 100px 120px 100px 40px;
  gap: 12px;
  align-items: end;
}

.item-description {
  grid-column: 1;
}

.item-type {
  grid-column: 2;
}

.item-quantity {
  grid-column: 3;
}

.item-price {
  grid-column: 4;
}

.item-total {
  grid-column: 5;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.item-total label {
  font-size: 12px;
  color: #8E8E93;
  font-weight: 500;
}

.total-amount {
  font-weight: 600;
  color: #1C1C1E;
  font-size: 14px;
}

.item-actions {
  grid-column: 6;
  display: flex;
  justify-content: center;
  align-items: end;
  padding-bottom: 8px;
}

.add-item-section {
  margin-top: 16px;
}

.add-item-btn {
  width: 100%;
  height: 48px;
  border-style: dashed;
  border-color: #6E56CF;
  color: #6E56CF;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.add-item-btn:hover {
  border-color: #5A47B8;
  color: #5A47B8;
  background-color: rgba(110, 86, 207, 0.05);
}

/* Totals section */
.totals-section {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.totals-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.totals-row:last-child {
  border-bottom: none;
  margin-top: 8px;
  padding-top: 16px;
  border-top: 2px solid #6E56CF;
}

.total-final {
  font-size: 18px;
  color: #1C1C1E;
}

/* Form actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px 0;
  border-top: 1px solid #f0f0f0;
}

.form-actions button {
  min-width: 120px;
}

/* Responsive */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .item-form {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .item-total {
    grid-column: 1;
    align-items: flex-start;
  }
  
  .item-actions {
    grid-column: 1;
    justify-content: flex-start;
  }
}
