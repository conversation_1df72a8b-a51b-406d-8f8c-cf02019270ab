import { Component, OnInit, signal } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';

// Ng-Zorro imports
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NgxPermissionsModule } from 'ngx-permissions';

import { MemberService } from '../../services/member.service';
import { SubscriptionService } from '../../services/subscription.service';
import { UiPermissionsService } from '../../services/ui-permissions.service';
import { ErrorService, ValidationMessages } from '../../services/error.service';
import { Member, CreateMemberRequest, UpdateMemberRequest, MemberType, MemberStatus } from '../../models/member.model';
import { SubscriptionPlan, MembershipType } from '../../models/subscription.model';

@Component({
  selector: 'app-member-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NzButtonModule,
    NzIconModule,
    NzInputModule,
    NzSelectModule,
    NzFormModule,
    NzCardModule,
    NzSpinModule,
    NzDatePickerModule,
    NzSwitchModule,
    NgxPermissionsModule
  ],
  templateUrl: './member-form.component.html',
  styleUrl: './member-form.component.css'
})
export class MemberFormComponent implements OnInit {

  // Signaux pour la réactivité
  private loadingSignal = signal<boolean>(false);
  private memberSignal = signal<Member | null>(null);

  // Getters pour les templates
  get loading() { return this.loadingSignal(); }
  get member() { return this.memberSignal(); }

  // Propriétés du composant
  isEditMode = false;
  memberId: string | null = null;
  memberForm!: FormGroup;

  // Options pour les selects
  memberTypeOptions = [
    { label: 'Étudiant', value: 'student' },
    { label: 'Professionnel', value: 'professional' },
    { label: 'Freelance', value: 'freelance' }
  ];

  statusOptions = [
    { label: 'Actif', value: 'active' },
    { label: 'Inactif', value: 'inactive' },
    { label: 'Suspendu', value: 'suspended' }
  ];

  subscriptions: SubscriptionPlan[] = [];

  validation_messages: ValidationMessages = {
    'firstName': [
      { type: 'required', message: 'Le prénom est obligatoire' },
      { type: 'minlength', message: 'Le prénom doit contenir au moins 2 caractères' }
    ],
    'lastName': [
      { type: 'required', message: 'Le nom est obligatoire' },
      { type: 'minlength', message: 'Le nom doit contenir au moins 2 caractères' }
    ],
    'email': [
      { type: 'required', message: 'L\'email est obligatoire' },
      { type: 'email', message: 'Format d\'email invalide' }
    ],
    'phone': [
      { type: 'required', message: 'Le téléphone est obligatoire' },
      { type: 'pattern', message: 'Le téléphone doit être au format marocain (06xxxxxxxx, 07xxxxxxxx ou +212xxxxxxxx)' }
    ],
    'memberType': [
      { type: 'required', message: 'Le type de membre est obligatoire' }
    ],
    'subscriptionType': [
      { type: 'required', message: 'Le type d\'abonnement est obligatoire' }
    ]
  };

  // Enums pour les templates
  MemberType = MemberType;
  MemberStatus = MemberStatus;

  constructor(
    private fb: FormBuilder,
    private memberService: MemberService,
    private subscriptionService: SubscriptionService,
    private router: Router,
    private route: ActivatedRoute,
    private location: Location,
    private message: NzMessageService,
    public uiPermissions: UiPermissionsService,
    private errorService: ErrorService
  ) {
    this.initializeForm();
  }

  ngOnInit() {
    // Vérifier si on est en mode édition
    this.memberId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.memberId;

    // Charger les subscriptions seulement si l'utilisateur a les permissions
    if (this.uiPermissions.canShowSubscriptionColumn()) {
      this.loadSubscriptions();
    }

    if (this.isEditMode && this.memberId) {
      this.loadMember(this.memberId);
    }
  }

  private initializeForm() {
    const formConfig: any = {
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, Validators.pattern(/^(06|07)\d{8}$|^\+212[67]\d{8}$/)]],
      memberType: [null, [Validators.required]],
      isActive: [true], // Utiliser un boolean pour le switch
      company: [''], // Nom de l'entreprise
      studentCode: [''],
      iceNumber: ['']
    };

    // Ajouter le champ abonnement seulement si l'utilisateur a les permissions
    if (this.uiPermissions.canShowSubscriptionColumn()) {
      formConfig.subscriptionType = [null]; // Rendu optionnel
    }

    this.memberForm = this.fb.group(formConfig);

    // Surveiller les changements de type de membre pour la validation et les abonnements
    this.memberForm.get('memberType')?.valueChanges.subscribe(type => {
      const studentCodeControl = this.memberForm.get('studentCode');
      const iceNumberControl = this.memberForm.get('iceNumber');
      const companyControl = this.memberForm.get('company');
      const subscriptionControl = this.memberForm.get('subscriptionType');

      // Réinitialiser les validations
      studentCodeControl?.clearValidators();
      studentCodeControl?.setValue('');
      iceNumberControl?.clearValidators();
      iceNumberControl?.setValue('');
      companyControl?.clearValidators();
      companyControl?.setValue('');

      // Toujours réinitialiser l'abonnement sélectionné lors du changement de type
      subscriptionControl?.setValue('');

      // Appliquer les validations selon le type
      if (type === MemberType.STUDENT) {
        studentCodeControl?.setValidators([Validators.required]);
      } else if (type === MemberType.COMPANY) {
        iceNumberControl?.setValidators([Validators.required]);
        companyControl?.setValidators([Validators.required]);
      }

      studentCodeControl?.updateValueAndValidity();
      iceNumberControl?.updateValueAndValidity();
      companyControl?.updateValueAndValidity();
    });
  }

  private loadMember(id: string) {
    this.loadingSignal.set(true);
    this.memberService.getMemberById(id).subscribe({
      next: (member) => {
        if (member) {
          this.memberSignal.set(member);
          this.populateForm(member);
        } else {
          this.message.error('Membre non trouvé');
          this.router.navigate(['/members']);
        }
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement du membre:', error);
        this.message.error('Erreur lors du chargement du membre');
        this.loadingSignal.set(false);
        this.router.navigate(['/members']);
      }
    });
  }

  private populateForm(member: Member) {
    const formValue: any = {
      firstName: member.firstName,
      lastName: member.lastName,
      email: member.email,
      phone: member.phone,
      memberType: member.memberType,
      isActive: member.status === MemberStatus.ACTIVE,
      company: member.company || '',
      studentCode: member.studentCode || '',
      iceNumber: member.iceNumber || ''
    };

    // Ajouter l'abonnement seulement si l'utilisateur a les permissions
    if (this.uiPermissions.canShowSubscriptionColumn()) {
      // Trouver l'ID de l'abonnement basé sur le nom
      let subscriptionId = '';
      if (member.subscriptionType && member.subscriptionType.trim() !== '' && member.subscriptionType !== '-') {
        const subscription = this.subscriptions.find(s => s.name === member.subscriptionType);
        subscriptionId = subscription ? subscription.id : '';
      }
      formValue.subscriptionType = subscriptionId;
    }

    this.memberForm.patchValue(formValue);
  }

  onSubmit() {
    if (this.memberForm.valid) {
      this.loadingSignal.set(true);

      if (this.isEditMode && this.memberId) {
        this.updateMember();
      } else {
        this.createMember();
      }
    } else {
      const validationMessage = this.errorService.getFormValidationMessage(this.memberForm);
      this.message.warning(validationMessage);
      this.markFormGroupTouched();
    }
  }

  private createMember() {
    const formValue = this.memberForm.value;
    const request: CreateMemberRequest = {
      firstName: formValue.firstName,
      lastName: formValue.lastName,
      email: formValue.email,
      phone: formValue.phone,
      memberType: formValue.memberType,
      subscriptionId: this.uiPermissions.canShowSubscriptionColumn() ? formValue.subscriptionType : '', // Utiliser subscriptionType du form seulement si permissions
      company: formValue.memberType === MemberType.COMPANY ? formValue.company : undefined,
      studentCode: formValue.memberType === MemberType.STUDENT ? formValue.studentCode : undefined,
      iceNumber: formValue.memberType === MemberType.COMPANY ? formValue.iceNumber : undefined
    };

    this.memberService.createMember(request).subscribe({
      next: (member) => {
        this.message.success('Membre créé avec succès');
        this.router.navigate(['/members']);
      },
      error: (error) => {
        console.error('Erreur lors de la création:', error);

        // Afficher directement le message d'erreur du backend
        const errorMessage = error?.error?.message || error?.message || 'Erreur lors de la création du membre';
        this.message.error(errorMessage);
        this.loadingSignal.set(false);
      }
    });
  }

  private updateMember() {
    const formValue = this.memberForm.value;
    const request: UpdateMemberRequest = {
      firstName: formValue.firstName,
      lastName: formValue.lastName,
      email: formValue.email,
      phone: formValue.phone,
      memberType: formValue.memberType,
      subscriptionId: this.uiPermissions.canShowSubscriptionColumn() ? formValue.subscriptionType : undefined, // Utiliser subscriptionType du form seulement si permissions
      status: formValue.isActive ? MemberStatus.ACTIVE : MemberStatus.INACTIVE,
      company: formValue.memberType === MemberType.COMPANY ? formValue.company : undefined,
      studentCode: formValue.memberType === MemberType.STUDENT ? formValue.studentCode : undefined,
      iceNumber: formValue.memberType === MemberType.COMPANY ? formValue.iceNumber : undefined
    };

    this.memberService.updateMember(this.memberId!, request).subscribe({
      next: (member) => {
        this.message.success('Membre modifié avec succès');
        this.router.navigate(['/members']);
      },
      error: (error) => {
        console.error('Erreur lors de la modification:', error);

        // Afficher directement le message d'erreur du backend
        const errorMessage = error?.error?.message || error?.message || 'Erreur lors de la modification du membre';
        this.message.error(errorMessage);
        this.loadingSignal.set(false);
      }
    });
  }

  onCancel() {
    this.location.back();
  }

  private markFormGroupTouched() {
    this.errorService.markFormGroupTouched(this.memberForm);
  }

  // Méthodes utilitaires pour la validation
  isFieldInvalid(fieldName: string): boolean {
    return this.errorService.isFieldInvalid(fieldName, this.memberForm);
  }

  getErrorMessage(controlName: string): string {
    return this.errorService.getErrorMessage(controlName, this.memberForm, this.validation_messages);
  }

  getPageTitle(): string {
    return this.isEditMode ? 'Modifier le membre' : 'Nouveau membre';
  }

  getPageIcon(): string {
    return this.isEditMode ? 'edit' : 'user-add';
  }

  // Charger les subscriptions depuis le backend
  private loadSubscriptions() {
    this.subscriptionService.getPlans().subscribe({
      next: (plans) => {
        this.subscriptions = plans;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des abonnements:', error);
        this.message.error('Erreur lors du chargement des abonnements');
        // Fallback avec des données par défaut en cas d'erreur
        this.subscriptions = [];
      }
    });
  }

  // Filtrer les subscriptions par type de membre
  getSubscriptionsByType(memberType: MemberType | null) {
    if (!memberType) return [];

    // Convertir MemberType vers MembershipType pour la compatibilité
    let membershipType: MembershipType;
    switch (memberType) {
      case MemberType.STUDENT:
        membershipType = MembershipType.STUDENT;
        break;
      case MemberType.PROFESSIONAL:
        membershipType = MembershipType.PROFESSIONAL;
        break;
      case MemberType.COMPANY:
        membershipType = MembershipType.COMPANY;
        break;
      default:
        return [];
    }

    return this.subscriptions.filter(sub =>
      sub.membershipTypes.includes(membershipType) || sub.membershipTypes.length === 0
    );
  }
}
