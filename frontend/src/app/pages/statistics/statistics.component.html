<div class="statistics-container">

  <!-- En-tête avec titre et navigation temporelle -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <nz-icon nzType="bar-chart" nzTheme="outline"></nz-icon>
        Statistiques
      </h1>
      <p class="page-description">Analyse financière en temps réel</p>
    </div>

    <div class="header-actions">
      <div class="period-selector">
        <button
          class="period-btn"
          [class.active]="selectedPeriod === 'month'"
          (click)="onPeriodChange('month')">
          Mois
        </button>
        <button
          class="period-btn"
          [class.active]="selectedPeriod === 'quarter'"
          (click)="onPeriodChange('quarter')">
          Trimestre
        </button>
        <button
          class="period-btn"
          [class.active]="selectedPeriod === 'year'"
          (click)="onPeriodChange('year')">
          Année
        </button>
      </div>
    </div>
  </div>

  <!-- Métriques principales -->
  <div class="metrics-grid">
    <div class="metric-card stat-card revenue">
      <div class="metric-icon">
        <i nz-icon nzType="dollar" nzTheme="outline"></i>
      </div>
      <div class="metric-content">
        <span class="metric-label">Revenus Total</span>
        <span class="metric-value">{{ formatCurrency(totalRevenue) }}</span>
        <span class="metric-change positive">+12.5%</span>
      </div>
    </div>

    <div class="metric-card stat-card paid">
      <div class="metric-icon">
        <i nz-icon nzType="check-circle" nzTheme="outline"></i>
      </div>
      <div class="metric-content">
        <span class="metric-label">Factures Payées</span>
        <span class="metric-value">{{ formatCurrency(totalPaid) }}</span>
        <span class="metric-change positive">+8.3%</span>
      </div>
    </div>

    <div class="metric-card stat-card unpaid">
      <div class="metric-icon">
        <i nz-icon nzType="clock-circle" nzTheme="outline"></i>
      </div>
      <div class="metric-content">
        <span class="metric-label">En Attente</span>
        <span class="metric-value">{{ formatCurrency(totalUnpaid) }}</span>
        <span class="metric-change negative">-2.1%</span>
      </div>
    </div>

    <div class="metric-card stat-card rate">
      <div class="metric-icon">
        <i nz-icon nzType="rise" nzTheme="outline"></i>
      </div>
      <div class="metric-content">
        <span class="metric-label">Taux de Paiement</span>
        <span class="metric-value">{{ paymentRate }}%</span>
        <div class="progress-ring">
          <svg class="progress-svg" width="60" height="60">
            <circle
              cx="30"
              cy="30"
              r="25"
              stroke="#E5E5EA"
              stroke-width="4"
              fill="none">
            </circle>
            <circle
              cx="30"
              cy="30"
              r="25"
              stroke="#6E56CF"
              stroke-width="4"
              fill="none"
              [style.stroke-dasharray]="157"
              [style.stroke-dashoffset]="157 - (157 * paymentRate / 100)"
              class="progress-circle">
            </circle>
          </svg>
        </div>
      </div>
    </div>
  </div>

  <!-- Graphiques principaux - nouvelle disposition -->
  <div class="charts-section">

    <!-- Graphique des factures par mois - pleine largeur -->
    <div class="main-chart">
      <div class="chart-card stat-card">
        <div class="chart-header">
          <h3><i nz-icon nzType="credit-card" nzTheme="outline"></i> Factures Mensuelles</h3>
          <div class="chart-legend">
            <span class="legend-item paid">
              <span class="legend-dot"></span>
              Payées
            </span>
            <span class="legend-item unpaid">
              <span class="legend-dot"></span>
              En attente
            </span>
          </div>
        </div>

      <div class="chart-container">
        <div class="chart-bars">
          <div
            class="bar-group"
            *ngFor="let month of monthlyData"
            (mouseenter)="onMonthHover(month.month)"
            (mouseleave)="onMonthHover(null)"
            [class.hovered]="hoveredMonth === month.month">

            <div class="bar-stack">
              <div
                class="bar paid"
                [style.height.%]="getPaidPercentage(month)">
              </div>
              <div
                class="bar unpaid"
                [style.height.%]="getUnpaidPercentage(month)">
              </div>
            </div>

            <span class="bar-label">{{ month.month }}</span>

            <!-- Tooltip au hover -->
            <div class="bar-tooltip" *ngIf="hoveredMonth === month.month">
              <div class="tooltip-content">
                <div class="tooltip-item">
                  <span class="tooltip-label">Payées:</span>
                  <span class="tooltip-value">{{ formatCurrency(month.paid) }}</span>
                </div>
                <div class="tooltip-item">
                  <span class="tooltip-label">En attente:</span>
                  <span class="tooltip-value">{{ formatCurrency(month.unpaid) }}</span>
                </div>
                <div class="tooltip-total">
                  <span class="tooltip-label">Total:</span>
                  <span class="tooltip-value">{{ formatCurrency(month.total) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Graphique des revenus par type - en horizontal -->
    <div class="revenue-section">
      <div class="chart-card stat-card">
        <div class="chart-header">
          <h3><i nz-icon nzType="pie-chart" nzTheme="outline"></i> Répartition des Revenus</h3>
          <span class="total-amount">{{ formatCurrency(totalRevenue) }}</span>
        </div>

      <div class="revenue-chart">
        <div class="revenue-items">
          <div
            class="revenue-item"
            *ngFor="let item of revenueData"
            (mouseenter)="onRevenueHover(item.type)"
            (mouseleave)="onRevenueHover(null)"
            [class.hovered]="hoveredRevenue === item.type">

            <div class="revenue-info">
              <div class="revenue-header">
                <div class="revenue-left">
                  <span class="revenue-icon" [style.background-color]="item.color + '20'" [style.color]="item.color">
                    <i nz-icon [nzType]="item.icon"></i>
                  </span>
                  <span class="revenue-type">{{ item.type }}</span>
                </div>
                <span class="revenue-percentage">{{ item.percentage }}%</span>
              </div>

              <div class="revenue-amount">{{ formatCurrency(item.amount) }}</div>

              <div class="revenue-bar">
                <div
                  class="revenue-progress"
                  [style.width.%]="item.percentage"
                  [style.background-color]="item.color">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Insights et tendances -->
  <div class="insights-section">
    <div class="insight-card stat-card">
      <div class="insight-icon">
        <i nz-icon nzType="rocket" nzTheme="outline"></i>
      </div>
      <div class="insight-content">
        <h4>Croissance Exceptionnelle</h4>
        <p>Les revenus ont augmenté de <strong>12.5%</strong> ce mois-ci, principalement grâce aux frais d'adhésion.</p>
      </div>
    </div>

    <div class="insight-card stat-card">
      <div class="insight-icon">
        <i nz-icon nzType="thunderbolt" nzTheme="outline"></i>
      </div>
      <div class="insight-content">
        <h4>Paiements Rapides</h4>
        <p>Le taux de paiement de <strong>{{ paymentRate }}%</strong> est excellent et en amélioration constante.</p>
      </div>
    </div>

    <div class="insight-card stat-card">
      <div class="insight-icon">
        <i nz-icon nzType="trophy" nzTheme="outline"></i>
      </div>
      <div class="insight-content">
        <h4>Objectif Atteint</h4>
        <p>L'objectif mensuel de <strong>25,000 MAD</strong> a été dépassé de 20% ce mois-ci.</p>
      </div>
    </div>
  </div>

</div>
