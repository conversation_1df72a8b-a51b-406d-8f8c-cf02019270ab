/* Page container */
.reservation-details-container {
  padding: 0;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  background: transparent;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px 0;
  background: transparent;
  border-bottom: none;
}

.header-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.back-button {
  height: 48px !important;
  border-radius: 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
  background: white !important;
  border: 2px solid #E5E5EA !important;
  color: #1C1C1E !important;
  font-weight: 500 !important;
  padding: 0 16px !important;
}

.back-button:hover {
  border-color: #6E56CF !important;
  color: #6E56CF !important;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title nz-icon {
  color: #6E56CF;
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  color: #8E8E93;
  margin: 0;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.action-button {
  height: 48px !important;
  border-radius: 12px !important;
  font-weight: 500 !important;
  padding: 0 24px !important;
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

/* Content grid */
.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.main-info-card,
.space-info-card {
  grid-column: span 1;
}

.member-info-card,
.details-card {
  grid-column: span 1;
}

.history-card,
.actions-card {
  grid-column: span 2;
}

/* Cards */
.main-info-card,
.space-info-card,
.member-info-card,
.details-card,
.history-card,
.actions-card {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #E5E5EA !important;
}

.main-info-card .ant-card-head,
.space-info-card .ant-card-head,
.member-info-card .ant-card-head,
.details-card .ant-card-head,
.history-card .ant-card-head,
.actions-card .ant-card-head {
  border-bottom: 1px solid #E5E5EA !important;
  padding: 0 24px !important;
}

.main-info-card .ant-card-body,
.space-info-card .ant-card-body,
.member-info-card .ant-card-body,
.details-card .ant-card-body,
.history-card .ant-card-body,
.actions-card .ant-card-body {
  padding: 24px !important;
}

/* Reservation info */
.reservation-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-row {
  display: flex;
  gap: 32px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.info-item label {
  font-size: 12px;
  color: #8E8E93;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item .value {
  font-size: 16px;
  color: #1C1C1E;
  font-weight: 600;
}

.status-tag {
  align-self: flex-start;
  font-weight: 500 !important;
  border-radius: 6px !important;
}

/* Space info */
.space-info {
  padding: 0;
}

.space-header {
  margin-bottom: 20px;
}

.space-details {
  flex: 1;
}

.space-name {
  font-size: 18px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0 0 4px 0;
}

.space-location {
  font-size: 14px;
  color: #8E8E93;
  margin: 0 0 4px 0;
}

.space-capacity {
  font-size: 14px;
  color: #6E56CF;
  font-weight: 500;
  margin: 0;
}

.space-amenities h4 {
  font-size: 14px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0 0 12px 0;
}

.amenities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.amenity-tag {
  background: #F0F5FF !important;
  color: #6E56CF !important;
  border: 1px solid #E5E5EA !important;
  border-radius: 6px !important;
  font-size: 12px !important;
}

/* Member info */
.member-info {
  padding: 0;
}

.member-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.member-details {
  flex: 1;
}

.member-name {
  font-size: 20px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0 0 4px 0;
  cursor: pointer;
  transition: color 0.2s ease;
}

.member-name:hover {
  color: #6E56CF;
}

.member-email {
  font-size: 14px;
  color: #8E8E93;
  margin: 0;
}

/* Additional details */
.additional-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item label {
  font-size: 12px;
  color: #8E8E93;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 14px;
  color: #1C1C1E;
  margin: 0;
  line-height: 1.5;
}

/* Timeline */
.history-card .ant-timeline {
  margin-top: 16px;
}

.timeline-date {
  font-size: 12px;
  color: #8E8E93;
}

/* Quick actions */
.quick-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.quick-action-btn {
  height: 40px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  padding: 0 16px !important;
  font-size: 14px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

/* Loading and error states */
.loading-state,
.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content {
  margin-top: 16px;
  color: #8E8E93;
  font-size: 16px;
}

/* Responsive */
@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
  }

  .main-info-card,
  .space-info-card,
  .member-info-card,
  .details-card,
  .history-card,
  .actions-card {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .reservation-details-container {
    padding: 0 20px;
  }

  .page-header {
    flex-direction: column;
    gap: 20px;
    text-align: left;
    padding: 20px 0;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
  }

  .header-actions {
    width: 100%;
    justify-content: stretch;
    flex-direction: column;
    gap: 12px;
  }

  .header-actions button {
    width: 100% !important;
    justify-content: center !important;
  }

  .info-row {
    flex-direction: column;
    gap: 16px;
  }

  .space-header {
    flex-direction: column;
    text-align: center;
  }

  .action-button {
    width: 100%;
  }

  .quick-actions {
    flex-direction: column;
  }

  .quick-action-btn {
    width: 100%;
  }
}
