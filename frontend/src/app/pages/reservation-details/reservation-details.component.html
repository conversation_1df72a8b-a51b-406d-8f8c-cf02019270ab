<div class="reservation-details-container">
  <!-- En-tête -->
  <div class="page-header">
    <div class="header-content">
      <div class="title-section">
        <h1 class="page-title">
          <nz-icon nzType="calendar" nzTheme="outline"></nz-icon>
          Détails de la réservation
        </h1>
        <p class="page-description">{{ reservation?.purpose || 'Réservation' }}</p>
      </div>
    </div>
    <div class="header-actions">
      <button nz-button nzType="text" nzSize="large" (click)="goBack()" class="back-button">
        <nz-icon nzType="arrow-left"></nz-icon>
        Retour
      </button>
      <button nz-button nzType="default" nzSize="large" (click)="editReservation()" class="action-button">
        <nz-icon nzType="edit"></nz-icon>
        Modifier
      </button>
      <button
        nz-button
        nzType="primary"
        nzSize="large"
        (click)="confirmReservation()"
        [disabled]="reservation?.status === 'confirmed'"
        class="action-button"
      >
        <nz-icon nzType="check-circle"></nz-icon>
        {{ reservation?.status === 'confirmed' ? 'Confirmée' : 'Confirmer' }}
      </button>
    </div>
  </div>

  <div class="content-grid" *ngIf="reservation">
    <!-- Informations principales -->
    <nz-card class="main-info-card" nzTitle="Informations principales">
      <div class="reservation-info">
        <div class="info-row">
          <div class="info-item">
            <label>Date de début</label>
            <span class="value">{{ getFormattedFullDate(reservation.startTime) }}</span>
          </div>
          <div class="info-item">
            <label>Date de fin</label>
            <span class="value">{{ getFormattedFullDate(reservation.endTime) }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <label>Heure de début</label>
            <span class="value">{{ reservation.startTime | date:'HH:mm' }}</span>
          </div>
          <div class="info-item">
            <label>Heure de fin</label>
            <span class="value">{{ reservation.endTime | date:'HH:mm' }}</span>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <label>Durée</label>
            <span class="value">{{ getDuration() }}</span>
          </div>
          <div class="info-item">
            <label>Statut</label>
            <nz-tag [nzColor]="getStatusColor(reservation.status)" class="status-tag">
              {{ getStatusLabel(reservation.status) }}
            </nz-tag>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <label>Nombre de personnes</label>
            <span class="value">{{ reservation.numberOfPeople || 1 }} personne(s)</span>
          </div>
          <div class="info-item" *ngIf="reservation.purpose">
            <label>Objet</label>
            <span class="value">{{ reservation.purpose }}</span>
          </div>
        </div>
      </div>
    </nz-card>

    <!-- Informations de l'espace -->
    <nz-card class="space-info-card" nzTitle="Espace réservé">
      <div class="space-info">
        <div class="space-header">
          <div class="space-details">
            <h3 class="space-name">{{ reservation.spaceName }}</h3>
            <p class="space-location">{{ spaceInfo?.location || 'Emplacement non spécifié' }}</p>
            <p class="space-capacity">Capacité : {{ spaceInfo?.capacity || 'N/A' }} personnes</p>
          </div>
        </div>

        <div class="space-amenities" *ngIf="spaceInfo?.amenities?.length">
          <h4>Équipements disponibles</h4>
          <div class="amenities-list">
            <nz-tag *ngFor="let amenity of spaceInfo?.amenities" class="amenity-tag">
              {{ amenity }}
            </nz-tag>
          </div>
        </div>
      </div>
    </nz-card>

    <!-- Informations du member -->
    <nz-card class="member-info-card" nzTitle="Informations du member">
      <div class="member-info">
        <div class="member-header">
          <div class="member-avatar">
            <nz-avatar [nzSize]="64" [nzText]="getMemberInitials()" nzIcon="user"></nz-avatar>
          </div>
          <div class="member-details">
            <h3 class="member-name" (click)="navigateToMember()">{{ reservation.userName }}</h3>
            <p class="member-email">{{ reservation.userEmail }}</p>
          </div>
        </div>
      </div>
    </nz-card>

    <!-- Détails supplémentaires -->
    <nz-card class="details-card" nzTitle="Détails supplémentaires">
      <div class="additional-details">
        <div class="detail-item">
          <label>Objet de la réservation</label>
          <p class="detail-value">{{ reservation.purpose }}</p>
        </div>

        <div class="detail-item" *ngIf="reservation?.notes">
          <label>Notes additionnelles</label>
          <p class="detail-value">{{ reservation.notes }}</p>
        </div>

        <div class="detail-item" *ngIf="reservation?.recurrence && reservation.recurrence !== 'none'">
          <label>Récurrence</label>
          <p class="detail-value">{{ getRecurrenceLabel(reservation.recurrence!) }}</p>
        </div>
      </div>
    </nz-card>

    <!-- Historique -->
    <nz-card class="history-card" nzTitle="Historique">
      <nz-timeline>
        <nz-timeline-item nzColor="blue">
          <p>Réservation créée</p>
          <span class="timeline-date">{{ getFormattedDateTime(reservation.createdAt) }}</span>
        </nz-timeline-item>
        <nz-timeline-item nzColor="orange" *ngIf="reservation.status !== 'pending'">
          <p>Réservation confirmée</p>
          <span class="timeline-date">{{ getFormattedDateTime(reservation.updatedAt) }}</span>
        </nz-timeline-item>
        <nz-timeline-item nzColor="green" *ngIf="reservation.status === 'confirmed'">
          <p>Prête pour utilisation</p>
          <span class="timeline-date">{{ getFormattedDateTime(reservation.startTime) }}</span>
        </nz-timeline-item>
      </nz-timeline>
    </nz-card>

    <!-- Actions rapides -->
    <nz-card class="actions-card" nzTitle="Actions rapides">
      <div class="quick-actions">
        <button nz-button nzType="default" (click)="duplicateReservation()" class="quick-action-btn">
          <nz-icon nzType="copy"></nz-icon>
          Dupliquer
        </button>
        <button nz-button nzType="default" (click)="sendNotification()" class="quick-action-btn">
          <nz-icon nzType="mail"></nz-icon>
          Notifier le member
        </button>
        <button nz-button nzType="default" nzDanger (click)="cancelReservation()" class="quick-action-btn">
          <nz-icon nzType="close-circle"></nz-icon>
          Annuler
        </button>
      </div>
    </nz-card>
  </div>

  <!-- État de chargement -->
  <div class="loading-state" *ngIf="loading">
    <nz-spin nzSize="large">
      <div class="loading-content">Chargement des détails de la réservation...</div>
    </nz-spin>
  </div>

  <!-- État d'erreur -->
  <div class="error-state" *ngIf="error">
    <nz-result
      nzStatus="error"
      nzTitle="Erreur de chargement"
      [nzSubTitle]="error"
    >
      <div nz-result-extra>
        <button nz-button nzType="primary" (click)="goBack()">Retour</button>
      </div>
    </nz-result>
  </div>
</div>
