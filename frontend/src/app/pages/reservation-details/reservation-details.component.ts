import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Location, CommonModule } from '@angular/common';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ReservationService } from '../../services/reservation.service';
import { SpaceService } from '../../services/space.service';
import { Reservation } from '../../models/reservation.model';
import { Space } from '../../models/space.model';

// Imports Ng-Zorro
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzAlertModule } from 'ng-zorro-antd/alert';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzDescriptionsModule } from 'ng-zorro-antd/descriptions';
import { NzTimelineModule } from 'ng-zorro-antd/timeline';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzTypographyModule } from 'ng-zorro-antd/typography';
import { NzResultModule } from 'ng-zorro-antd/result';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzMessageModule } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-reservation-details',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzButtonModule,
    NzIconModule,
    NzTagModule,
    NzDividerModule,
    NzSpinModule,
    NzAlertModule,
    NzAvatarModule,
    NzDescriptionsModule,
    NzTimelineModule,
    NzGridModule,
    NzTypographyModule,
    NzResultModule,
    NzModalModule,
    NzMessageModule
  ],
  templateUrl: './reservation-details.component.html',
  styleUrls: ['./reservation-details.component.css']
})
export class ReservationDetailsComponent implements OnInit {
  reservation: Reservation | null = null;
  spaceInfo: Space | null = null;
  loading = false;
  error: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private location: Location,
    private message: NzMessageService,
    private modal: NzModalService,
    private reservationService: ReservationService,
    private spaceService: SpaceService
  ) {}

  ngOnInit() {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.loadReservation(id);
    } else {
      this.error = 'ID de réservation manquant';
    }
  }

  private loadReservation(id: string) {
    this.loading = true;
    this.error = null;

    this.reservationService.getReservationById(id).subscribe({
      next: (reservation) => {
        this.reservation = reservation;
        if (reservation && reservation.spaceId) {
          this.loadSpaceInfo(reservation.spaceId);
        } else {
          this.loading = false;
        }
      },
      error: (error) => {
        console.error('Error loading reservation:', error);
        this.error = 'Erreur lors du chargement de la réservation';
        this.loading = false;
      }
    });
  }

  private loadSpaceInfo(spaceId: string) {
    this.spaceService.getSpaceById(spaceId).subscribe({
      next: (space) => {
        this.spaceInfo = space;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading space info:', error);
        this.loading = false;
      }
    });
  }

  goBack() {
    this.location.back();
  }

  navigateToMember() {
    if (this.reservation) {
      this.router.navigate(['/members/details', this.reservation.memberId || this.reservation.userId]);
    }
  }

  editReservation() {
    if (this.reservation) {
      this.router.navigate(['/reservation-form'], {
        queryParams: { id: this.reservation.id }
      });
    }
  }

  confirmReservation() {
    if (!this.reservation || this.reservation.status === 'confirmed') return;

    this.modal.confirm({
      nzTitle: 'Confirmer la réservation',
      nzContent: 'Voulez-vous confirmer cette réservation ?',
      nzOkText: 'Confirmer',
      nzCancelText: 'Annuler',
      nzCentered: true,
      nzOnOk: () => {
        if (this.reservation) {
          this.reservationService.updateReservation(this.reservation.id, {
            ...this.reservation,
            status: 'confirmed'
          }).subscribe({
            next: (updatedReservation) => {
              this.reservation = updatedReservation;
              this.message.success('Réservation confirmée');
            },
            error: (error) => {
              console.error('Error confirming reservation:', error);
              this.message.error('Erreur lors de la confirmation');
            }
          });
        }
      }
    });
  }

  cancelReservation() {
    if (!this.reservation || this.reservation.status === 'cancelled') return;

    this.modal.confirm({
      nzTitle: 'Annuler la réservation',
      nzContent: 'Êtes-vous sûr de vouloir annuler cette réservation ? Cette action est irréversible.',
      nzOkText: 'Annuler la réservation',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzCancelText: 'Retour',
      nzCentered: true,
      nzOnOk: () => {
        if (this.reservation) {
          this.reservationService.updateReservation(this.reservation.id, {
            ...this.reservation,
            status: 'cancelled'
          }).subscribe({
            next: (updatedReservation) => {
              this.reservation = updatedReservation;
              this.message.success('Réservation annulée');
              this.goBack();
            },
            error: (error) => {
              console.error('Error cancelling reservation:', error);
              this.message.error('Erreur lors de l\'annulation');
            }
          });
        }
      }
    });
  }

  // Méthodes utilitaires pour l'affichage
  getFormattedFullDate(date: Date | string | undefined): string {
    if (!date) return '-';

    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      if (isNaN(dateObj.getTime())) return '-';
      return dateObj.toLocaleDateString('fr-FR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return '-';
    }
  }

  getFormattedDateTime(date: Date | string | undefined): string {
    if (!date) return '-';

    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      if (isNaN(dateObj.getTime())) return '-';
      return dateObj.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return '-';
    }
  }

  getDuration(): string {
    if (!this.reservation?.startTime || !this.reservation?.endTime) return '-';

    try {
      const start = new Date(this.reservation.startTime);
      const end = new Date(this.reservation.endTime);
      const diffMs = end.getTime() - start.getTime();
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

      if (diffHours > 0) {
        return `${diffHours}h${diffMinutes > 0 ? ` ${diffMinutes}min` : ''}`;
      }
      return `${diffMinutes}min`;
    } catch {
      return '-';
    }
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'pending': return 'orange';
      case 'confirmed': return 'green';
      case 'cancelled': return 'red';
      case 'completed': return 'blue';
      default: return 'default';
    }
  }

  getStatusLabel(status: string): string {
    switch (status) {
      case 'pending': return 'En attente';
      case 'confirmed': return 'Confirmée';
      case 'cancelled': return 'Annulée';
      case 'completed': return 'Terminée';
      default: return status;
    }
  }

  getRecurrenceLabel(recurrence: string): string {
    switch (recurrence) {
      case 'daily': return 'Quotidienne';
      case 'weekly': return 'Hebdomadaire';
      case 'monthly': return 'Mensuelle';
      case 'none': return 'Aucune';
      default: return recurrence;
    }
  }

  getMemberInitials(): string {
    if (!this.reservation?.userName) return 'U';

    const names = this.reservation.userName.split(' ');
    return names.map(name => name.charAt(0)).join('').toUpperCase().substring(0, 2);
  }

  duplicateReservation() {
    if (this.reservation) {
      this.router.navigate(['/reservation-form'], {
        queryParams: { duplicate: this.reservation.id }
      });
    }
  }

  sendNotification() {
    this.message.info('Fonctionnalité de notification à venir');
  }
}
