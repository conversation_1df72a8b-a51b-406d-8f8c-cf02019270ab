/* Page container */
.billing-container {
  padding: 0;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  background: transparent;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px 0;
  background: transparent;
  border-bottom: none;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title nz-icon {
  color: #6E56CF;
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  color: #8E8E93;
  margin: 0;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.add-button,
.generate-button {
  height: 48px !important;
  min-height: 48px !important;
  max-height: 48px !important;
  border-radius: 12px !important;
  font-weight: 500 !important;
  padding: 0 24px !important;
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  min-width: 140px !important;
  justify-content: center !important;
  line-height: 1 !important;
}

.add-button {
  background: #6E56CF !important;
  border: none !important;
  color: white !important;
}

.add-button:hover {
  background: #5A47B8 !important;
  color: white !important;
  transform: translateY(-1px);
}

.generate-button {
  background: white !important;
  border: 2px solid #E5E5EA !important;
  color: #1C1C1E !important;
}

.generate-button:hover {
  border-color: #6E56CF !important;
  color: #6E56CF !important;
  transform: translateY(-1px);
}

/* Force la hauteur pour les boutons Ant Design */
.header-actions .ant-btn-lg {
  height: 48px !important;
  min-height: 48px !important;
  max-height: 48px !important;
  line-height: 46px !important;
}

/* Stats section */
.stats-section {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.stat-card {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #E5E5EA !important;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
}

.stat-card.revenue {
  border-left: 4px solid #52C41A;
}

.stat-card.monthly {
  border-left: 4px solid #1890FF;
}

.stat-card.pending {
  border-left: 4px solid #FA8C16;
}

.stat-card.overdue {
  border-left: 4px solid #FF4D4F;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.revenue .stat-icon {
  background: rgba(82, 196, 26, 0.1);
  color: #52C41A;
}

.monthly .stat-icon {
  background: rgba(24, 144, 255, 0.1);
  color: #1890FF;
}

.pending .stat-icon {
  background: rgba(250, 140, 22, 0.1);
  color: #FA8C16;
}

.overdue .stat-icon {
  background: rgba(255, 77, 79, 0.1);
  color: #FF4D4F;
}

.stat-details {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1C1C1E;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #8E8E93;
  font-weight: 500;
}

/* Filters */
.filters-card {
  margin-bottom: 24px;
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #E5E5EA !important;
  background: white;
}

.filters-card .ant-card-head {
  border-bottom: 1px solid #E5E5EA !important;
  padding: 0 24px !important;
}

.filters-card .ant-card-body {
  padding: 24px !important;
}

.filters-row {
  display: flex;
  gap: 24px;
  align-items: end;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
  flex: 1;
}

.filter-item label {
  font-size: 14px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0;
}

.filter-item .ant-input-group {
  border-radius: 6px !important;
}

.filter-item .ant-input {
  border-radius: 6px !important;
}

.filter-item .ant-btn {
  border-radius: 0 6px 6px 0 !important;
}

.filter-actions {
  display: flex;
  gap: 12px;
  margin-left: auto;
  flex-wrap: wrap;
}

.filter-actions button {
  height: 32px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  padding: 0 16px !important;
  font-size: 14px !important;
}

/* Invoices table */
.invoices-card {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #E5E5EA !important;
}

.invoices-card .ant-card-head {
  border-bottom: 1px solid #E5E5EA !important;
  padding: 0 24px !important;
}

.invoices-card .ant-card-body {
  padding: 24px !important;
}

/* Table styles */
.ant-table {
  border-radius: 8px;
  overflow: hidden;
}

/* Largeurs fixes des colonnes et gestion du contenu long */
.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  white-space: normal !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
  vertical-align: top !important;
}

/* Styles spécifiques par colonne */
.member-info {
  max-width: 180px;
  word-wrap: break-word;
}

.member-name {
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 2px;
  line-height: 1.3;
}

.member-email {
  font-size: 12px;
  color: #8E8E93;
  margin-bottom: 2px;
  line-height: 1.3;
}

.member-company {
  font-size: 11px;
  color: #8E8E93;
  font-style: italic;
  line-height: 1.3;
}

.amount-info {
  text-align: right;
}

.total-amount {
  font-weight: 600;
  color: #1C1C1E;
  font-size: 14px;
}

/* Container responsive pour le tableau */
.table-container {
  position: relative;
}

.table-wrapper {
  overflow-x: auto !important;
  -webkit-overflow-scrolling: touch;
  width: 100%;
}

/* Menu d'actions dropdown */
.action-dropdown {
  display: flex;
  justify-content: center;
}

.action-trigger {
  width: 32px !important;
  height: 32px !important;
  border-radius: 6px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #8E8E93 !important;
  transition: all 0.2s ease;
}

.action-trigger:hover {
  color: #6E56CF !important;
  background-color: #EDE9F8 !important;
  transform: translateY(-1px);
}

/* Actions principales */
.primary-action {
  margin-right: 8px;
}

.primary-action button {
  font-size: 12px;
  padding: 4px 12px;
  height: 32px;
  border-radius: 6px;
}

/* Menu dropdown items */
.ant-dropdown-menu-item {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 8px 12px !important;
}

.ant-dropdown-menu-item:hover {
  background-color: #F8F9FA !important;
}

.ant-dropdown-menu-item.danger-item {
  color: #ff4d4f !important;
}

.ant-dropdown-menu-item.danger-item:hover {
  background-color: #fff2f0 !important;
}

.ant-table-thead > tr > th {
  background: #FAFAFA !important;
  border-bottom: 2px solid #E5E5EA !important;
  font-weight: 600 !important;
  color: #1C1C1E !important;
  padding: 16px !important;
}

.ant-table-tbody > tr > td {
  padding: 16px !important;
  border-bottom: 1px solid #F0F0F0 !important;
}

.ant-table-tbody > tr:hover > td {
  background: #F8F9FA !important;
}

/* Member info in table */
.member-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.clickable-member {
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.clickable-member:hover {
  background-color: #F0F5FF;
}

.member-name {
  font-weight: 600;
  color: #1C1C1E;
  font-size: 14px;
}

.clickable-member:hover .member-name {
  color: #6E56CF;
}

/* Numéro de facture cliquable */
.clickable-invoice-number {
  cursor: pointer;
  color: #6E56CF;
  transition: all 0.2s ease;
  text-decoration: none;
  border-radius: 4px;
  padding: 4px 8px;
  margin: -4px -8px;
  display: inline-block;
}

.clickable-invoice-number:hover {
  background-color: rgba(110, 86, 207, 0.1);
  color: #5A47B8;
  text-decoration: underline;
}

.member-email {
  font-size: 12px;
  color: #8E8E93;
}

.member-company {
  font-size: 12px;
  color: #6E56CF;
  font-weight: 500;
}

/* Amount info in table */
.amount-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  text-align: right;
}

.total-amount {
  font-weight: 600;
  color: #1C1C1E;
  font-size: 14px;
}

.tax-info {
  font-size: 12px;
  color: #8E8E93;
}

/* Action buttons */
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.action-buttons button {
  width: 32px !important;
  height: 32px !important;
  border-radius: 6px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Tags personnalisés */
.ant-tag {
  border-radius: 6px !important;
  font-weight: 500 !important;
  border: none !important;
  font-size: 12px !important;
  padding: 2px 8px !important;
  line-height: 1.4 !important;
}

/* Responsive */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Responsive pour tablettes */
@media (max-width: 992px) {
  .billing-container {
    padding: 0 16px;
  }

  .page-title {
    font-size: 26px;
  }

  .header-actions {
    flex-direction: row;
    gap: 12px;
  }

  .header-actions button {
    flex: 1;
    min-width: 120px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .filters-row {
    flex-wrap: wrap;
    gap: 16px;
  }

  .filter-item {
    min-width: 180px;
  }

  .filter-actions {
    margin-left: 0;
    width: 100%;
    justify-content: flex-end;
    flex-wrap: wrap;
    margin-top: 8px;
  }
}

@media (max-width: 768px) {
  .billing-container {
    padding: 0 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    text-align: left;
    padding: 16px 0;
  }

  .header-content {
    text-align: left;
  }

  .page-title {
    font-size: 24px;
  }

  .page-description {
    font-size: 14px;
  }

  .header-actions {
    width: 100%;
    justify-content: stretch;
    flex-direction: column;
    gap: 12px;
  }

  .add-button,
  .generate-button {
    width: 100% !important;
    justify-content: center !important;
    height: 48px !important;
    font-size: 16px !important;
    font-weight: 500 !important;
  }

  /* Stats responsive */
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stat-card {
    margin-bottom: 0 !important;
  }

  .stat-content {
    padding: 12px !important;
  }

  .stat-icon {
    width: 40px !important;
    height: 40px !important;
    font-size: 18px !important;
  }

  .stat-title {
    font-size: 12px !important;
  }

  .stat-value {
    font-size: 20px !important;
  }

  /* Filtres responsive */
  .filters-card {
    margin-bottom: 16px;
  }

  .filters-card .ant-card-body {
    padding: 16px !important;
  }

  .filters-row {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filter-item {
    min-width: auto;
    flex: none;
    width: 100%;
  }

  .filter-item label {
    font-size: 14px;
    margin-bottom: 6px;
    display: block;
  }

  .filter-actions {
    margin-left: 0;
    justify-content: flex-start;
    width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 16px;
  }

  .filter-actions button {
    flex: 1 !important;
    justify-content: center !important;
    height: 44px !important;
    font-size: 14px !important;
  }

  /* Table responsive avec scroll horizontal */
  .invoices-card {
    margin-bottom: 16px;
  }

  .invoices-card .ant-card-body {
    padding: 0 !important;
    overflow: hidden;
  }

  .table-container {
    position: relative;
  }

  .table-wrapper {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch;
    width: 100%;
    scrollbar-width: thin;
    scrollbar-color: #E5E5EA transparent;
    margin-bottom: 16px; /* Espace pour la pagination */
  }

  .table-wrapper::-webkit-scrollbar {
    height: 8px;
  }

  .table-wrapper::-webkit-scrollbar-track {
    background: #F8F9FA;
    border-radius: 4px;
  }

  .table-wrapper::-webkit-scrollbar-thumb {
    background: #E5E5EA;
    border-radius: 4px;
  }

  .table-wrapper::-webkit-scrollbar-thumb:hover {
    background: #D1D1D6;
  }

  .ant-table {
    min-width: 900px !important; /* Largeur ajustée pour les nouvelles colonnes */
    font-size: 12px;
  }

  /* Pagination fixe en bas */
  .ant-table-pagination {
    position: sticky !important;
    bottom: 0 !important;
    background: white !important;
    padding: 16px 24px !important;
    border-top: 1px solid #f0f0f0 !important;
    margin: 0 !important;
    z-index: 10 !important;
  }

  /* Colonne sticky pour Member */
  .ant-table-thead > tr > th:nth-child(2),
  .ant-table-tbody > tr > td:nth-child(2) {
    position: sticky !important;
    left: 0 !important;
    z-index: 9 !important;
    background: white !important;
    box-shadow: 2px 0 4px rgba(0,0,0,0.1) !important;
  }

  .ant-table-thead > tr > th:nth-child(2) {
    background: #fafafa !important;
    z-index: 11 !important;
  }

  .ant-table-thead > tr > th {
    font-size: 12px;
    padding: 8px 12px;
    white-space: nowrap;
  }

  .ant-table-tbody > tr > td {
    font-size: 12px;
    padding: 8px 12px;
    white-space: normal !important; /* Permet les retours à la ligne */
    word-wrap: break-word !important;
    word-break: break-word !important;
    vertical-align: top !important;
  }

  .clickable-invoice-number {
    font-size: 12px !important;
  }

  /* Actions avec dropdown */
  .action-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
    min-width: 100px;
    width: 100%;
  }

  .action-dropdown {
    display: flex;
    justify-content: center;
  }

  .action-trigger {
    width: 32px !important;
    height: 32px !important;
    border-radius: 6px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: #8E8E93 !important;
    transition: all 0.2s ease;
  }

  .action-trigger:hover {
    color: #6E56CF !important;
    background-color: #EDE9F8 !important;
    transform: translateY(-1px);
  }
}

/* Responsive pour très petits écrans */
@media (max-width: 480px) {
  .billing-container {
    padding: 0 8px;
  }

  .page-header {
    padding: 12px 0;
  }

  .page-title {
    font-size: 20px;
  }

  .page-description {
    font-size: 13px;
  }

  .header-actions button {
    height: 44px !important;
    font-size: 15px !important;
  }

  .stats-grid {
    gap: 8px;
  }

  .stat-content {
    padding: 10px !important;
  }

  .stat-icon {
    width: 36px !important;
    height: 36px !important;
    font-size: 16px !important;
  }

  .stat-title {
    font-size: 11px !important;
  }

  .stat-value {
    font-size: 18px !important;
  }

  .filters-card .ant-card-body {
    padding: 12px !important;
  }

  .filter-actions button {
    height: 40px !important;
    font-size: 13px !important;
  }

  /* Table très petit écran */
  .ant-table {
    min-width: 800px !important; /* Largeur réduite mais toujours lisible */
    font-size: 11px;
  }

  /* Colonne sticky pour très petits écrans */
  .ant-table-thead > tr > th:nth-child(2),
  .ant-table-tbody > tr > td:nth-child(2) {
    position: sticky !important;
    left: 0 !important;
    z-index: 9 !important;
    background: white !important;
    box-shadow: 2px 0 4px rgba(0,0,0,0.1) !important;
  }

  .ant-table-thead > tr > th:nth-child(2) {
    background: #fafafa !important;
    z-index: 11 !important;
  }

  .ant-table-thead > tr > th {
    font-size: 11px;
    padding: 6px 8px;
  }

  .ant-table-tbody > tr > td {
    font-size: 11px;
    padding: 6px 8px;
  }

  .clickable-invoice-number {
    font-size: 11px !important;
  }

  .action-trigger {
    width: 28px !important;
    height: 28px !important;
  }

  /* Améliorer l'indicateur de scroll */
  .invoices-card::before {
    content: "← Faites défiler horizontalement pour voir toutes les colonnes →";
    display: block;
    text-align: center;
    font-size: 11px;
    color: #8E8E93;
    padding: 8px;
    background: #F8F9FA;
    border-radius: 4px;
    margin-bottom: 8px;
  }
}
