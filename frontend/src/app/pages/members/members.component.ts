import { Component, OnInit, OnDestroy, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NgxPermissionsModule } from 'ngx-permissions';

import { MemberService } from '../../services/member.service';
import { SubscriptionService } from '../../services/subscription.service';
import { SiteContextService } from '../../services/site-context.service';
import { UiPermissionsService } from '../../services/ui-permissions.service';
import { Member, MemberType, MemberStatus, Subscription, CreateMemberRequest, UpdateMemberRequest } from '../../models/member.model';
import { SubscriptionPlan } from '../../models/subscription.model';
import { PageRequest, PageResponse, PageRequestBuilder, PaginationState } from '../../models/pagination.model';

@Component({
  selector: 'app-members',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NzTableModule,
    NzButtonModule,
    NzIconModule,
    NzInputModule,
    NzSelectModule,
    NzModalModule,
    NzFormModule,
    NzTagModule,
    NzCardModule,
    NzSpinModule,
    NzToolTipModule,
    NzDropDownModule,
    NzModalModule,
    NzMessageModule,
    NgxPermissionsModule
  ],
  templateUrl: './members.component.html',
  styleUrl: './members.component.css'
})
export class MembersComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Signaux pour la gestion d'état
  private membersSignal = signal<Member[]>([]);
  private subscriptionsSignal = signal<SubscriptionPlan[]>([]);
  private searchTermSignal = signal<string>('');
  private selectedStatusSignal = signal<MemberStatus | 'all'>('all');
  private selectedTypeSignal = signal<MemberType | 'all'>('all');
  private selectedSubscriptionSignal = signal<string | 'all'>('all');

  // FormControl pour la recherche avec debounce
  searchControl = new FormControl('');

  // Signaux pour la pagination
  private paginationSignal = signal<PaginationState>({
    currentPage: 1,
    pageSize: 10,
    total: 0,
    loading: false
  });

  // Signaux pour le tri
  private sortFieldSignal = signal<string>('lastModifiedDate');
  private sortDirectionSignal = signal<'ASC' | 'DESC'>('DESC');



  // Signaux calculés (filtrage seulement, le tri est géré par ng-zorro)
  filteredMembers = computed(() => {
    const members = this.membersSignal();
    const searchTerm = this.searchTermSignal().toLowerCase();
    const selectedStatus = this.selectedStatusSignal();
    const selectedType = this.selectedTypeSignal();
    const selectedSubscription = this.selectedSubscriptionSignal();

    return members.filter(member => {
      const matchesSearch = !searchTerm ||
        member.firstName.toLowerCase().includes(searchTerm) ||
        member.lastName.toLowerCase().includes(searchTerm) ||
        member.email.toLowerCase().includes(searchTerm) ||
        (member.studentCode && member.studentCode.toLowerCase().includes(searchTerm));

      const matchesStatus = selectedStatus === 'all' || member.status === selectedStatus;
      const matchesType = selectedType === 'all' || member.memberType === selectedType;

      // Ne filtrer par abonnement que si l'utilisateur a les permissions
      const matchesSubscription = !this.uiPermissions.canShowSubscriptionColumn() ||
        selectedSubscription === 'all' ||
        member.subscriptionType === selectedSubscription;

      return matchesSearch && matchesStatus && matchesType && matchesSubscription;
    });
  });

  // Propriétés pour les modales (propriétés normales pour le binding bidirectionnel)
  isCreateModalVisible = false;
  isEditModalVisible = false;
  selectedMember: Member | null = null;

  // Formulaires
  createForm: FormGroup;
  editForm: FormGroup;

  // Enums pour les templates
  MemberType = MemberType;
  MemberStatus = MemberStatus;

  // Options pour les selects
  statusOptions = [
    { label: 'Tous', value: 'all' },
    { label: 'Actif', value: MemberStatus.ACTIVE },
    { label: 'Inactif', value: MemberStatus.INACTIVE }
  ];

  typeOptions = [
    { label: 'Tous', value: 'all' },
    { label: 'Étudiant', value: MemberType.STUDENT },
    { label: 'Professionnel', value: MemberType.PROFESSIONAL },
    { label: 'Entreprise', value: MemberType.COMPANY }
  ];

  constructor(
    private memberService: MemberService,
    private subscriptionService: SubscriptionService,
    private fb: FormBuilder,
    private message: NzMessageService,
    private router: Router,
    private modal: NzModalService,
    private siteContext: SiteContextService,
    public uiPermissions: UiPermissionsService
  ) {
    const createFormConfig: any = {
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, Validators.pattern(/^(06|07)\d{8}$|^\+212[67]\d{8}$/)]],
      memberType: [MemberType.PROFESSIONAL, [Validators.required]],
      studentCode: [''],
      iceNumber: ['']
    };

    // Ajouter le champ abonnement seulement si l'utilisateur a les permissions
    if (this.uiPermissions.canShowSubscriptionColumn()) {
      createFormConfig.subscriptionId = ['', [Validators.required]];
    }

    this.createForm = this.fb.group(createFormConfig);

    const editFormConfig: any = {
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, Validators.pattern(/^(06|07)\d{8}$|^\+212[67]\d{8}$/)]],
      status: ['', [Validators.required]],
      studentCode: [''],
      iceNumber: ['']
    };

    // Ajouter le champ abonnement seulement si l'utilisateur a les permissions
    if (this.uiPermissions.canShowSubscriptionColumn()) {
      editFormConfig.subscriptionId = ['', [Validators.required]];
    }

    this.editForm = this.fb.group(editFormConfig);

    // Surveiller les changements de type de membre pour afficher/masquer les champs spécifiques
    this.createForm.get('memberType')?.valueChanges.subscribe(type => {
      const studentCodeControl = this.createForm.get('studentCode');
      const iceNumberControl = this.createForm.get('iceNumber');

      // Réinitialiser tous les champs spécifiques
      studentCodeControl?.clearValidators();
      studentCodeControl?.setValue('');
      iceNumberControl?.clearValidators();
      iceNumberControl?.setValue('');

      // Appliquer les validations selon le type
      if (type === MemberType.STUDENT) {
        studentCodeControl?.setValidators([Validators.required]);
      } else if (type === MemberType.COMPANY) {
        iceNumberControl?.setValidators([Validators.required]);
      }

      studentCodeControl?.updateValueAndValidity();
      iceNumberControl?.updateValueAndValidity();
    });
  }

  ngOnInit() {
    this.loadMembers();

    // Charger les abonnements seulement si l'utilisateur a les permissions
    if (this.uiPermissions.canShowSubscriptionColumn()) {
      this.loadSubscriptions();
    }

    // S'abonner aux changements de site pour recharger les données
    this.siteContext.currentSite$
      .pipe(takeUntil(this.destroy$))
      .subscribe(site => {
        if (site) {
          console.log('🔄 Site changed, reloading members data for:', site.name);
          this.loadMembers();

          // Recharger les abonnements seulement si l'utilisateur a les permissions
          if (this.uiPermissions.canShowSubscriptionColumn()) {
            this.loadSubscriptions();
          }
        }
      });

    // S'abonner aux changements de recherche avec debounce
    this.searchControl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(searchTerm => {
        this.searchTermSignal.set(searchTerm || '');
        this.paginationSignal.update(state => ({ ...state, currentPage: 1 }));
        this.loadMembers();
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Chargement des données avec pagination
  loadMembers() {
    const pagination = this.paginationSignal();
    this.paginationSignal.update(state => ({ ...state, loading: true }));

    const pageRequest = PageRequestBuilder.create()
      .page(pagination.currentPage - 1) // Convertir en 0-based
      .size(pagination.pageSize)
      .sortBy(this.sortFieldSignal()) // Utiliser le tri actuel
      .sortDirection(this.sortDirectionSignal())
      .build();

    // Utiliser les nouveaux paramètres
    const status = this.selectedStatusSignal() !== 'all' ? this.selectedStatusSignal() : undefined;
    const searchTerm = this.searchTermSignal();
    const memberType = this.selectedTypeSignal() !== 'all' ? this.selectedTypeSignal() : undefined;

    // Conversion robuste du subscriptionId - seulement si l'utilisateur a les permissions
    let subscriptionId: number | undefined = undefined;
    if (this.uiPermissions.canShowSubscriptionColumn()) {
      const selectedSubscription = this.selectedSubscriptionSignal();
      if (selectedSubscription !== 'all') {
        const parsedId = parseInt(selectedSubscription);
        if (!isNaN(parsedId)) {
          subscriptionId = parsedId;
        }
      }
    }

    console.log('Loading members with filters:', { status, searchTerm, memberType, subscriptionId }); // Debug

    this.memberService.getMembersPaginated(pageRequest, status, searchTerm, memberType, subscriptionId).subscribe({
      next: (pageResponse) => {
        this.membersSignal.set(pageResponse.content);
        this.paginationSignal.update(state => ({
          ...state,
          total: pageResponse.totalElements,
          loading: false
        }));
      },
      error: (error) => {
        console.error('Error loading members:', error);
        this.paginationSignal.update(state => ({ ...state, loading: false }));
      }
    });
  }

  loadSubscriptions() {
    console.log('Loading subscriptions for member filter...'); // Debug

    // Charger les abonnements dans le MemberService ET dans le composant
    const pageRequest = PageRequestBuilder.create()
      .page(0)
      .size(100) // Récupérer beaucoup d'abonnements pour le filtre
      .sortBy('name')
      .sortDirection('ASC')
      .build();

    // Charger dans le composant pour le filtre
    this.subscriptionService.getPlansPaginated(pageRequest, true).subscribe({
      next: (pageResponse) => {
        console.log('Subscriptions page loaded:', pageResponse); // Debug
        this.subscriptionsSignal.set(pageResponse.content);
      },
      error: (error) => {
        console.error('Error loading subscriptions for member filter:', error);
        this.subscriptionsSignal.set([]);
      }
    });

    // Charger aussi dans le MemberService pour l'inférence des types d'abonnement
    this.memberService.loadAllSubscriptions().subscribe({
      next: (subscriptions) => {
        console.log('Subscriptions loaded in MemberService:', subscriptions.length); // Debug
      },
      error: (error) => {
        console.error('Error loading subscriptions in MemberService:', error);
      }
    });
  }

  // Getters pour les signaux (pour les templates)
  get members() { return this.membersSignal(); }
  get subscriptions() { return this.subscriptionsSignal(); }
  get pagination() { return this.paginationSignal(); }
  get loading() { return this.paginationSignal().loading; }

  // Getters pour les filtres
  get selectedStatus() { return this.selectedStatusSignal(); }
  get selectedType() { return this.selectedTypeSignal(); }
  get selectedSubscription() { return this.selectedSubscriptionSignal(); }

  // Méthodes pour les templates (pour éviter les erreurs de binding)
  isLoading() { return this.paginationSignal().loading; }
  getFilteredMembers() { return this.filteredMembers(); }

  // Filtres
  onSearchChange(searchTerm: string) {
    // Mettre à jour le FormControl qui déclenchera automatiquement la recherche avec debounce
    this.searchControl.setValue(searchTerm, { emitEvent: true });
  }

  onStatusFilterChange(status: MemberStatus | 'all') {
    this.selectedStatusSignal.set(status);
    // Recharger les données avec le nouveau filtre
    this.paginationSignal.update(state => ({ ...state, currentPage: 1 }));
    this.loadMembers();
  }

  onTypeFilterChange(type: MemberType | 'all') {
    this.selectedTypeSignal.set(type);
    // Recharger les données avec le nouveau filtre
    this.paginationSignal.update(state => ({ ...state, currentPage: 1 }));
    this.loadMembers();
  }

  onSubscriptionFilterChange(subscription: string | 'all') {
    this.selectedSubscriptionSignal.set(subscription);
    // Recharger les données avec le nouveau filtre
    this.paginationSignal.update(state => ({ ...state, currentPage: 1 }));
    this.loadMembers();
  }

  // Effacer tous les filtres
  clearFilters() {
    this.searchTermSignal.set('');
    this.selectedStatusSignal.set('all');
    this.selectedTypeSignal.set('all');
    this.selectedSubscriptionSignal.set('all');

    // Réinitialiser le FormControl de recherche
    this.searchControl.setValue('', { emitEvent: false });

    // Réinitialiser la pagination à la première page
    this.paginationSignal.update(state => ({ ...state, currentPage: 1 }));

    // Recharger les données
    this.loadMembers();
  }

  // Gestion de la pagination
  onPageChange(page: number) {
    this.paginationSignal.update(state => ({ ...state, currentPage: page }));
    this.loadMembers();
  }

  onPageSizeChange(size: number) {
    this.paginationSignal.update(state => ({
      ...state,
      pageSize: size,
      currentPage: 1 // Retourner à la première page
    }));
    this.loadMembers();
  }

  // Gestion du tri
  onSort(field: string, direction: any) {
    // Convertir l'événement en direction de tri
    const sortDirection = direction as 'ascend' | 'descend' | null;

    if (sortDirection === null) {
      // Pas de tri - retour au tri par défaut
      this.sortFieldSignal.set('lastModifiedDate');
      this.sortDirectionSignal.set('DESC');
    } else {
      // Mapper les champs frontend vers les champs backend
      const backendField = this.mapFrontendFieldToBackend(field);
      this.sortFieldSignal.set(backendField);
      this.sortDirectionSignal.set(sortDirection === 'ascend' ? 'ASC' : 'DESC');
    }

    // Retourner à la première page et recharger
    this.paginationSignal.update(state => ({ ...state, currentPage: 1 }));
    this.loadMembers();
  }

  // Obtenir l'état du tri pour une colonne
  getSortOrder(field: string): 'ascend' | 'descend' | null {
    const backendField = this.mapFrontendFieldToBackend(field);
    if (this.sortFieldSignal() === backendField) {
      return this.sortDirectionSignal() === 'ASC' ? 'ascend' : 'descend';
    }
    return null;
  }

  // Mapper les champs frontend vers les champs backend
  private mapFrontendFieldToBackend(frontendField: string): string {
    const fieldMapping: { [key: string]: string } = {
      'updatedAt': 'lastModifiedDate',
      'createdAt': 'createdDate',
      'firstName': 'firstName',
      'lastName': 'lastName',
      'email': 'email',
      'memberType': 'memberType',
      'status': 'status'
    };

    return fieldMapping[frontendField] || frontendField;
  }



  // Navigation vers les formulaires
  navigateToCreate() {
    this.router.navigate(['/members/new']);
  }

  navigateToEdit(member: Member) {
    this.router.navigate(['/members/edit', member.id]);
  }

  navigateToSubscriptionDetails(member: Member) {
    if (member.subscriptionType && member.subscriptionType.trim() !== '' && member.subscriptionType !== '-') {
      // Trouver l'ID de l'abonnement basé sur le nom
      const subscription = this.subscriptions.find(s => s.name === member.subscriptionType);
      if (subscription) {
        this.router.navigate(['/subscriptions/details', subscription.id]);
      } else {
        this.message.warning('Détails de l\'abonnement non disponibles');
      }
    }
  }



  showMemberDetails(member: Member) {
    this.router.navigate(['/members/details', member.id]);
  }





  confirmDeleteMember(member: Member) {
    this.modal.confirm({
      nzTitle: 'Supprimer le membre',
      nzContent: `Êtes-vous sûr de vouloir supprimer le membre <strong>${member.firstName} ${member.lastName}</strong> ?<br><br>Cette action est irréversible.`,
      nzOkText: 'Supprimer',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzCancelText: 'Annuler',
      nzCentered: true,
      nzOnOk: () => {
        this.deleteMember(member);
      }
    });
  }

  private deleteMember(member: Member) {
    this.memberService.deleteMember(member.id).subscribe({
      next: () => {
        this.message.success('Membre supprimé avec succès');
        this.loadMembers();
      },
      error: (error) => {
        this.message.error('Erreur lors de la suppression du membre');
      }
    });
  }

  // Utilitaires
  getStatusColor(status: MemberStatus): string {
    switch (status) {
      case MemberStatus.ACTIVE: return 'green';
      case MemberStatus.INACTIVE: return 'orange';
      default: return 'default';
    }
  }

  getFormattedDate(date: Date | string | undefined): string {
    if (!date) return '-';

    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      if (isNaN(dateObj.getTime())) return '-';

      return dateObj.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return '-';
    }
  }

  getStatusText(status: MemberStatus): string {
    switch (status) {
      case MemberStatus.ACTIVE: return 'Actif';
      case MemberStatus.INACTIVE: return 'Inactif';
      default: return status;
    }
  }

  getTypeText(type: MemberType): string {
    const typeMap = {
      [MemberType.STUDENT]: 'Étudiant',
      [MemberType.PROFESSIONAL]: 'Professionnel',
      [MemberType.COMPANY]: 'Entreprise'
    };
    return typeMap[type] || type;
  }

  getTypeColor(type: MemberType): string {
    const colorMap = {
      [MemberType.STUDENT]: 'blue',
      [MemberType.PROFESSIONAL]: 'green',
      [MemberType.COMPANY]: 'purple'
    };
    return colorMap[type] || 'default';
  }

  // Obtenir les abonnements filtrés par type
  getSubscriptionsByType(memberType: MemberType) {
    return this.subscriptions.filter(s =>
      !s.membershipTypes || s.membershipTypes.length === 0 ||
      s.membershipTypes.includes(memberType as any)
    );
  }


}
