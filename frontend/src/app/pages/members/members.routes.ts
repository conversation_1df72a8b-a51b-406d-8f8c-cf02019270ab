import { Routes } from '@angular/router';
import { MembersComponent } from './members.component';

export const MEMBERS_ROUTES: Routes = [
  { path: '', component: MembersComponent },
  {
    path: 'new',
    loadComponent: () => import('../member-form/member-form.component').then(m => m.MemberFormComponent)
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('../member-form/member-form.component').then(m => m.MemberFormComponent)
  },
  {
    path: 'details/:id',
    loadComponent: () => import('../member-details/member-details.component').then(m => m.MemberDetailsComponent)
  }
];
