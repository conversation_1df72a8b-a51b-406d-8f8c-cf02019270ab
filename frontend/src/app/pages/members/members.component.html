<div class="members-container">
  <!-- En-tête avec titre et bouton d'ajout -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <nz-icon nzType="team" nzTheme="outline"></nz-icon>
        Gestion des membres
      </h1>
      <p class="page-subtitle"><PERSON><PERSON>rez les membres étudiants et professionnels de votre espace de coworking</p>
    </div>
    <button nz-button nzType="primary" nzSize="large" (click)="navigateToCreate()" class="add-button">
      <nz-icon nzType="plus"></nz-icon>
      Nouveau membre
    </button>
  </div>

  <!-- Filtres et recherche -->
  <nz-card class="filters-card" nzTitle="Filtres">
    <div class="filters-row" [class.basic-plan]="!uiPermissions.canShowSubscriptionColumn()">
      <div class="filter-item">
        <label>Rechercher</label>
        <nz-input-group nzPrefixIcon="search">
          <input
            nz-input
            placeholder="Nom, email, code étudiant..."
            [formControl]="searchControl"
          />
        </nz-input-group>
      </div>

      <div class="filter-item">
        <label>Statut</label>
        <nz-select
          nzPlaceHolder="Tous les statuts"
          (ngModelChange)="onStatusFilterChange($event)"
          [ngModel]="selectedStatus"
        >
          <nz-option
            *ngFor="let option of statusOptions"
            [nzLabel]="option.label"
            [nzValue]="option.value"
          ></nz-option>
        </nz-select>
      </div>

      <div class="filter-item">
        <label>Type</label>
        <nz-select
          nzPlaceHolder="Tous les types"
          (ngModelChange)="onTypeFilterChange($event)"
          [ngModel]="selectedType"
        >
          <nz-option
            *ngFor="let option of typeOptions"
            [nzLabel]="option.label"
            [nzValue]="option.value"
          ></nz-option>
        </nz-select>
      </div>

      <div class="filter-item" *ngxPermissionsOnly="['CAN_MANAGE_SUBSCRIPTIONS']">
        <label>Abonnement</label>
        <nz-select
          nzPlaceHolder="Tous les abonnements"
          (ngModelChange)="onSubscriptionFilterChange($event)"
          [ngModel]="selectedSubscription"
        >
          <nz-option nzLabel="Tous" nzValue="all"></nz-option>
          <nz-option
            *ngFor="let subscription of subscriptions"
            [nzLabel]="subscription.name"
            [nzValue]="subscription.id"
          ></nz-option>
        </nz-select>
      </div>

      <div class="filter-item">
        <label>&nbsp;</label>
        <button
          nz-button
          nzType="default"
          (click)="clearFilters()"
          class="clear-filters-btn"
        >
          <nz-icon nzType="clear"></nz-icon>
          Effacer les filtres
        </button>
      </div>
    </div>
  </nz-card>

  <!-- Tableau des membres -->
  <nz-card class="table-card">
    <nz-spin [nzSpinning]="isLoading()">
      <div class="table-wrapper">
        <nz-table
          #sortTable
          [nzData]="members"
          [nzFrontPagination]="false"
          [nzTotal]="pagination.total"
          [nzPageIndex]="pagination.currentPage"
          [nzPageSize]="pagination.pageSize"
          [nzShowSizeChanger]="true"
          [nzPageSizeOptions]="[10, 20, 50]"
          nzShowQuickJumper
          (nzPageIndexChange)="onPageChange($event)"
          (nzPageSizeChange)="onPageSizeChange($event)"
          [nzScroll]="{ x: '1200px' }"
        >
        <thead>
          <tr>
            <th nzLeft="0px" nzWidth="200px"
                [nzSortOrder]="getSortOrder('firstName')"
                (nzSortOrderChange)="onSort('firstName', $event)">
              Membre
            </th>
            <th nzWidth="180px"
                [nzSortOrder]="getSortOrder('email')"
                (nzSortOrderChange)="onSort('email', $event)">
              Contact
            </th>
            <th nzWidth="100px"
                [nzSortOrder]="getSortOrder('memberType')"
                (nzSortOrderChange)="onSort('memberType', $event)">
              Type
            </th>
            <th nzWidth="120px"
                [nzSortOrder]="getSortOrder('subscriptionId')"
                (nzSortOrderChange)="onSort('subscriptionId', $event)"
                *ngxPermissionsOnly="['CAN_MANAGE_SUBSCRIPTIONS']">
              Abonnement
            </th>
            <th nzWidth="100px"
                [nzSortOrder]="getSortOrder('status')"
                (nzSortOrderChange)="onSort('status', $event)">
              Statut
            </th>
            <th nzWidth="120px"
                [nzSortOrder]="getSortOrder('studentCode')"
                (nzSortOrderChange)="onSort('studentCode', $event)">
              Code Étudiant
            </th>
            <th nzWidth="120px"
                [nzSortOrder]="getSortOrder('iceNumber')"
                (nzSortOrderChange)="onSort('iceNumber', $event)">
              ICE
            </th>
            <th nzWidth="120px"
                [nzSortOrder]="getSortOrder('updatedAt')"
                (nzSortOrderChange)="onSort('updatedAt', $event)">
              Date modification
            </th>
            <th nzWidth="80px">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let member of members">
            <td nzLeft="0px">
              <div class="member-info">
                <div class="member-avatar">
                  {{ member.firstName.charAt(0) }}{{ member.lastName.charAt(0) }}
                </div>
                <div class="member-details">
                  <div class="member-name">
                    <a (click)="showMemberDetails(member)" style="color: inherit; text-decoration: none; cursor: pointer;">
                      {{ member.firstName }} {{ member.lastName }}
                    </a>
                  </div>
                  <div class="member-id">#{{ member.id }}</div>
                </div>
              </div>
            </td>
            <td>
              <div class="contact-info">
                <div class="email">{{ member.email }}</div>
                <div class="phone">{{ member.phone }}</div>
              </div>
            </td>
            <td>
              <nz-tag [nzColor]="getTypeColor(member.memberType)">
                {{ getTypeText(member.memberType) }}
              </nz-tag>
            </td>
            <td *ngxPermissionsOnly="['CAN_MANAGE_SUBSCRIPTIONS']">
              <span
                *ngIf="member.subscriptionType && member.subscriptionType.trim() !== '' && member.subscriptionType !== '-'; else noSubscription"
                class="subscription-link"
                (click)="navigateToSubscriptionDetails(member)"
              >
                {{ member.subscriptionType }}
              </span>
              <ng-template #noSubscription>
                <span class="no-subscription">Aucun abonnement</span>
              </ng-template>
            </td>
            <td>
              <nz-tag [nzColor]="getStatusColor(member.status)">
                {{ getStatusText(member.status) }}
              </nz-tag>
            </td>
            <td>
              <span *ngIf="member.studentCode" class="student-code">{{ member.studentCode }}</span>
              <span *ngIf="!member.studentCode" class="no-code">-</span>
            </td>
            <td>
              <span *ngIf="member.iceNumber" class="ice-number">{{ member.iceNumber }}</span>
              <span *ngIf="!member.iceNumber" class="no-code">-</span>
            </td>
            <td>{{ getFormattedDate(member.updatedAt || member.createdAt) }}</td>
            <td>
              <div class="action-dropdown">
                <button
                  nz-button
                  nzType="text"
                  nzSize="small"
                  nz-dropdown
                  [nzDropdownMenu]="actionMenu"
                  nzPlacement="bottomRight"
                  class="action-trigger"
                >
                  <nz-icon nzType="more" nzTheme="outline"></nz-icon>
                </button>
                <nz-dropdown-menu #actionMenu="nzDropdownMenu">
                  <ul nz-menu>
                    <li nz-menu-item (click)="showMemberDetails(member)">
                      <nz-icon nzType="eye" nzTheme="outline"></nz-icon>
                      <span class="ml-1">Voir détails</span>
                    </li>
                    <li nz-menu-item (click)="navigateToEdit(member)">
                      <nz-icon nzType="edit" nzTheme="outline"></nz-icon>
                      <span class="ml-1">Modifier</span>
                    </li>
                    <li nz-menu-divider></li>
                    <li
                      nz-menu-item
                      class="danger-item"
                      (click)="confirmDeleteMember(member)"
                    >
                      <nz-icon nzType="delete" nzTheme="outline"></nz-icon>
                      <span class="ml-1">Supprimer</span>
                    </li>
                  </ul>
                </nz-dropdown-menu>
              </div>
            </td>
          </tr>
        </tbody>
      </nz-table>
      </div>
    </nz-spin>
  </nz-card>
</div>
