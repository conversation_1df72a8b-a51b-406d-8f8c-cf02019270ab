/* Container principal */
.subscriptions-container {
  padding: 0;
  max-width: 1400px;
  margin: 0 auto;
}

/* En-tête de page */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px 0;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title .anticon {
  color: #6E56CF;
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  color: #8E8E93;
  margin: 0;
  line-height: 1.4;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.add-button {
  height: 48px !important;
  border-radius: 12px !important;
  font-weight: 500 !important;
  font-size: 16px !important;
  padding: 0 24px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  background-color: #6E56CF !important;
  border-color: #6E56CF !important;
}

.add-button:hover {
  background-color: #5A47B8 !important;
  border-color: #5A47B8 !important;
  transform: translateY(-1px);
}

/* Cards */
.filters-card,
.table-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #E5E5EA;
}

.filters-card .ant-card-head,
.table-card .ant-card-head {
  border-bottom: 1px solid #E5E5EA;
  padding: 16px 24px;
}

.filters-card .ant-card-head-title,
.table-card .ant-card-head-title {
  font-size: 18px;
  font-weight: 600;
  color: #1C1C1E;
}

.filters-card .ant-card-body {
  padding: 24px;
}

.table-card .ant-card-body {
  padding: 0 !important;
}

/* Container responsive pour le tableau */
.table-wrapper {
  overflow-x: auto !important;
  -webkit-overflow-scrolling: touch;
  width: 100%;
}

/* Forcer le scroll horizontal */
.ant-table-container {
  overflow-x: auto !important;
}

/* Colonne sticky */
.ant-table-thead > tr > th[nz-left],
.ant-table-tbody > tr > td[nz-left] {
  position: sticky !important;
  left: 0 !important;
  z-index: 10 !important;
  background: #fff !important;
  box-shadow: 2px 0 4px rgba(0,0,0,0.1) !important;
}

.ant-table-thead > tr > th[nz-left] {
  background: #F8F9FA !important;
}

/* Tags généraux */
.ant-tag {
  border-radius: 6px !important;
  font-weight: 500 !important;
  font-size: 12px !important;
  padding: 2px 8px !important;
  border: none !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  white-space: normal !important;
  line-height: 1.2 !important;
}

/* Filtres */
.filters-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr auto;
  gap: 24px;
  align-items: end;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  font-weight: 500;
  color: #1C1C1E;
  margin: 0;
}

.filter-item .ant-input,
.filter-item .ant-select {
  border-radius: 8px;
}

/* Correction du double border pour le champ de recherche */
.filter-item .ant-input-group {
  border-radius: 8px;
}

.filter-item .ant-input-group .ant-input {
  border: none !important;
  box-shadow: none !important;
}

.filter-item .ant-input-group .ant-input:focus {
  border: none !important;
  box-shadow: none !important;
}

/* Bouton Clear Filters */
.clear-filters-btn {
  border-radius: 8px !important;
  border: 1px solid #D1D1D6 !important;
  color: #8E8E93 !important;
  background-color: #FFFFFF !important;
  height: 32px !important;
  padding: 0 12px !important;
  font-size: 14px !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
}

.clear-filters-btn:hover {
  border-color: #6E56CF !important;
  color: #6E56CF !important;
  background-color: #F8F9FA !important;
}

/* Tableau */
.ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.ant-table-thead > tr > th {
  background-color: #F8F9FA;
  border-bottom: 1px solid #E5E5EA;
  font-weight: 600;
  color: #1C1C1E;
  padding: 16px;
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.ant-table-tbody > tr > td {
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
  white-space: normal !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  vertical-align: top !important;
}

.ant-table-tbody > tr:hover > td {
  background-color: #F8F9FA;
}

/* Informations du plan */
.plan-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.plan-name {
  font-size: 16px;
  font-weight: 600;
  color: #1C1C1E;
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.plan-description {
  font-size: 14px;
  color: #8E8E93;
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.price-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.price {
  font-size: 16px;
  font-weight: 600;
  color: #6E56CF;
}

.duration {
  font-size: 14px;
  color: #1C1C1E;
  font-weight: 500;
}

/* Fonctionnalités */
.features-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.feature-tag {
  font-size: 12px;
  margin: 0;
  display: inline-block !important;
  width: auto !important;
  max-width: fit-content !important;
  padding: 2px 8px !important;
  border-radius: 4px !important;
}

.more-features {
  font-size: 12px;
  color: #8E8E93;
  font-style: italic;
}

/* Menu d'actions dropdown */
.action-dropdown {
  display: flex;
  justify-content: center;
}

.action-trigger {
  width: 32px !important;
  height: 32px !important;
  border-radius: 6px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #8E8E93 !important;
  transition: all 0.2s ease;
}

.action-trigger:hover {
  color: #6E56CF !important;
  background-color: #EDE9F8 !important;
  transform: translateY(-1px);
}

/* Styles pour les items du menu dropdown */
.ant-dropdown-menu {
  border-radius: 8px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid #E5E5EA !important;
  padding: 4px !important;
  min-width: 160px !important;
}

.ant-dropdown-menu .ant-dropdown-menu-item {
  border-radius: 6px !important;
  margin: 2px !important;
  padding: 12px 16px !important;
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 12px !important;
  font-size: 14px !important;
  color: #1C1C1E !important;
  white-space: nowrap !important;
  min-width: 150px !important;
  width: auto !important;
  box-sizing: border-box !important;
  height: auto !important;
  line-height: normal !important;
}

.ant-dropdown-menu .ant-dropdown-menu-item .anticon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
  display: inline-block !important;
  flex-shrink: 0 !important;
  vertical-align: middle !important;
}

.ant-dropdown-menu .ant-dropdown-menu-item span {
  display: inline-block !important;
  white-space: nowrap !important;
  vertical-align: middle !important;
  line-height: normal !important;
  margin-left: 4px !important;
}

.ant-dropdown-menu .ant-dropdown-menu-item:hover {
  background-color: #EDE9F8 !important;
  color: #6E56CF !important;
}

.ant-dropdown-menu .ant-dropdown-menu-item.danger-item {
  color: #FF3B30 !important;
}

.ant-dropdown-menu .ant-dropdown-menu-item.danger-item:hover {
  background-color: #FFE5E5 !important;
  color: #FF3B30 !important;
}

.ant-dropdown-menu .ant-dropdown-menu-divider {
  margin: 4px 0 !important;
  background-color: #E5E5EA !important;
}

/* Modales */
.ant-modal-header {
  border-bottom: 1px solid #E5E5EA;
  padding: 20px 24px;
}

.ant-modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #1C1C1E;
}

.ant-modal-body {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

/* Sections de formulaire */
.form-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #F0F0F0;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.form-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0 0 20px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #6E56CF;
  display: inline-block;
}

/* Éléments de formulaire */
.ant-form-item {
  margin-bottom: 20px;
}

.ant-form-item-label > label {
  font-size: 14px;
  font-weight: 500;
  color: #1C1C1E;
}

.ant-input,
.ant-input-number,
.ant-select-selector,
.ant-picker {
  border-radius: 8px;
  border: 1px solid #D1D1D6;
  transition: all 0.2s ease;
}

.ant-input:focus,
.ant-input-number:focus,
.ant-select-focused .ant-select-selector,
.ant-picker:focus {
  border-color: #6E56CF;
  box-shadow: 0 0 0 2px rgba(110, 86, 207, 0.1);
}

/* Créneaux horaires */
.time-slot-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  padding: 12px;
  background-color: #F8F9FA;
  border-radius: 8px;
  border: 1px solid #E5E5EA;
}

.time-separator {
  font-size: 14px;
  color: #8E8E93;
  font-weight: 500;
}

.add-time-slot-btn {
  width: 100%;
  margin-top: 8px;
  border-radius: 8px;
  border: 1px dashed #6E56CF;
  color: #6E56CF;
  background-color: transparent;
}

.add-time-slot-btn:hover {
  border-color: #6E56CF;
  color: #6E56CF;
  background-color: #EDE9F8;
}

/* Fonctionnalités */
.feature-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.feature-row input {
  flex: 1;
}

.add-feature-btn {
  width: 100%;
  margin-top: 8px;
  border-radius: 8px;
  border: 1px dashed #6E56CF;
  color: #6E56CF;
  background-color: transparent;
}

.add-feature-btn:hover {
  border-color: #6E56CF;
  color: #6E56CF;
  background-color: #EDE9F8;
}

/* Actions de modal */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #E5E5EA;
}

.modal-actions button {
  border-radius: 8px;
  padding: 8px 24px;
  height: auto;
  font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
  .subscriptions-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .add-button {
    width: 100%;
    justify-content: center !important;
  }

  .filters-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  /* Tableau responsive pour mobile */
  .ant-table {
    font-size: 12px;
  }

  .subscriptions-container {
    padding: 0 8px;
  }

  .plan-name {
    font-size: 14px;
  }

  .plan-description {
    font-size: 12px;
  }

  .time-slot-row {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .time-separator {
    text-align: center;
  }

  .feature-row {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  /* Améliorer l'affichage des tags sur mobile */
  .ant-tag {
    font-size: 10px !important;
    padding: 1px 6px !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    white-space: normal !important;
    line-height: 1.2 !important;
  }

  /* Réduire l'espacement des cellules sur mobile */
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 12px 8px !important;
  }
}
