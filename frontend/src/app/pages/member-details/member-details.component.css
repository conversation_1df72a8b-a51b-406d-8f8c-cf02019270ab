/* Page container */
.member-details-page {
  padding: 0;
  min-height: 100vh;
}

/* Page content */
.page-content {
  max-width: 1200px;
  margin: 0 auto;
}

.member-details-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

/* Header */
.details-header {
  padding: 32px;
  background: linear-gradient(135deg, #6E56CF 0%, #8B7ED8 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.member-avatar {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 32px;
  flex-shrink: 0;
  backdrop-filter: blur(10px);
}

.member-basic-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.member-name {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin: 0;
}

.member-id {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.member-badges {
  display: flex;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.header-actions button {
  border-radius: 8px !important;
  height: 40px !important;
  padding: 0 16px !important;
  font-weight: 500 !important;
}

/* Content */
.details-content {
  padding: 32px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Cards */
.info-card {
  border-radius: 12px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04) !important;
  border: 1px solid #E5E5EA !important;
}

.info-card .ant-card-head {
  border-bottom: 1px solid #E5E5EA !important;
  padding: 20px 24px !important;
}

.info-card .ant-card-head-title {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #1C1C1E !important;
}

.info-card .ant-card-body {
  padding: 24px !important;
}

/* Info grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item label {
  font-size: 14px;
  font-weight: 600;
  color: #8E8E93;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item span {
  font-size: 16px;
  color: #1C1C1E;
  font-weight: 500;
}

.student-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  background-color: #F0F0F0;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px !important;
}

.ice-number {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  background-color: #F3E8FF;
  color: #8B5CF6;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px !important;
  font-weight: 600;
}

/* Contact links */
.contact-link {
  color: #6E56CF !important;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.contact-link:hover {
  color: #5A45B8 !important;
  text-decoration: underline;
}

/* Subscriptions */
.subscriptions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.subscription-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #E5E5EA;
  border-radius: 8px;
  background-color: #FAFAFA;
}

.subscription-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0 0 8px 0;
}

.subscription-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin: 0;
}

.subscription-type {
  font-size: 14px;
  font-weight: 500;
  color: #6E56CF;
}

.subscription-dates {
  font-size: 14px;
  color: #8E8E93;
}

.subscription-status {
  flex-shrink: 0;
}

/* History */
.history-list {
  max-height: 400px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px 0;
}

.history-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.history-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.history-action {
  font-size: 16px;
  font-weight: 600;
  color: #1C1C1E;
}

.history-description {
  font-size: 14px;
  color: #8E8E93;
  line-height: 1.4;
}

.history-date {
  font-size: 12px;
  color: #C7C7CC;
  margin-top: 4px;
}

/* Loading */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content {
  width: 100px;
  height: 100px;
}

/* Responsive */
@media (max-width: 768px) {
  .member-details-page {
    padding: 16px;
  }

  .details-header {
    padding: 24px;
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .member-info {
    flex-direction: column;
    text-align: center;
  }

  .member-name {
    font-size: 24px;
  }

  .member-avatar {
    width: 64px;
    height: 64px;
    font-size: 24px;
  }

  .details-content {
    padding: 20px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .subscription-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }
}

/* Tags personnalisés */
.ant-tag {
  border-radius: 6px !important;
  font-weight: 500 !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
  border: none !important;
}

/* Empty states */
.ant-empty {
  padding: 40px 20px !important;
}

.ant-empty-description {
  color: #8E8E93 !important;
  font-size: 14px !important;
}

/* Payments section */
.payments-section {
  padding: 0;
}

.payments-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #E5E5EA;
}

.payments-stats {
  display: flex;
  gap: 32px;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #6E56CF;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #8E8E93;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.payments-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.payment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #FAFAFA;
  border-radius: 8px;
  border: 1px solid #E5E5EA;
  transition: all 0.2s ease;
}

.payment-item:hover {
  background: #F0F5FF;
  border-color: #6E56CF;
}

.payment-item.recent-payment {
  background: #F0F5FF;
  border-color: #6E56CF;
  position: relative;
}

.payment-item.recent-payment::before {
  content: 'RÉCENT';
  position: absolute;
  top: -8px;
  right: 16px;
  background: #6E56CF;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
  letter-spacing: 0.5px;
}

.payment-info {
  flex: 1;
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.payment-reference {
  font-weight: 600;
  color: #1C1C1E;
  font-size: 14px;
}

.payment-amount {
  font-weight: 700;
  color: #52C41A;
  font-size: 16px;
}

.payment-details {
  display: flex;
  gap: 24px;
  margin-bottom: 8px;
}

.payment-date,
.payment-method {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #8E8E93;
}

.payment-date nz-icon,
.payment-method nz-icon {
  color: #6E56CF;
}

.payment-notes {
  font-size: 12px;
  color: #8E8E93;
  font-style: italic;
  margin-top: 4px;
}

.payment-actions {
  margin-left: 16px;
}

.no-payments {
  text-align: center;
  padding: 40px 20px;
  color: #8E8E93;
}

.no-payments nz-icon {
  font-size: 48px;
  color: #E5E5EA;
  margin-bottom: 16px;
}

.no-payments p {
  margin: 0;
  font-size: 14px;
}

/* Responsive */
@media (max-width: 768px) {
  .member-details-page {
    padding: 0 16px;
  }

  .details-header {
    padding: 20px;
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .member-info {
    flex-direction: column;
    gap: 12px;
  }

  .member-avatar {
    width: 80px;
    height: 80px;
    font-size: 28px;
  }

  .member-name {
    font-size: 20px;
  }

  .member-email {
    font-size: 14px;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .details-content {
    padding: 16px;
  }

  .details-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .info-card .ant-card-body {
    padding: 16px !important;
  }

  .contact-info {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .subscription-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .history-item {
    gap: 12px;
  }

  .history-action {
    font-size: 14px;
  }

  /* Responsive pour l'historique des paiements */
  .payments-stats {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .stat-item {
    padding: 12px;
    background: #F8F9FA;
    border-radius: 8px;
  }

  .stat-value {
    font-size: 20px;
  }

  .payment-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 12px;
  }

  .payment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .payment-amount {
    font-size: 18px;
    align-self: flex-end;
  }

  .payment-details {
    flex-direction: column;
    gap: 8px;
  }

  .payment-actions {
    margin-left: 0;
    align-self: flex-end;
  }

  .payment-actions button {
    width: 100px;
  }
}
