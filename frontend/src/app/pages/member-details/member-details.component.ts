import { Component, OnInit, signal } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzListModule } from 'ng-zorro-antd/list';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';

import { NzMessageService } from 'ng-zorro-antd/message';

import { Member, MemberType, MemberStatus, MemberHistory, HistoryType } from '../../models/member.model';
import { MemberService } from '../../services/member.service';
import { PaymentHistory } from '../../models/invoice.model';
import { BillingService } from '../../services/billing.service';
import { PdfService } from '../../services/pdf.service';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-member-details',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzTagModule,
    NzButtonModule,
    NzIconModule,
    NzDividerModule,
    NzListModule,
    NzSpinModule,
    NzEmptyModule,
    NzToolTipModule,

  ],
  templateUrl: './member-details.component.html',
  styleUrl: './member-details.component.css'
})
export class MemberDetailsComponent implements OnInit {
  // Signaux pour la réactivité
  private memberSignal = signal<Member | null>(null);
  private loadingSignal = signal<boolean>(false);
  private historySignal = signal<MemberHistory[]>([]);
  private loadingHistorySignal = signal<boolean>(false);
  private paymentHistorySignal = signal<PaymentHistory[]>([]);

  // Getters pour les templates
  get member() {
    return this.memberSignal();
  }

  get loading() {
    return this.loadingSignal();
  }

  get history() {
    return this.historySignal();
  }

  get loadingHistory() {
    return this.loadingHistorySignal();
  }

  get paymentHistory() {
    return this.paymentHistorySignal();
  }

  // Enums pour les templates
  MemberType = MemberType;
  MemberStatus = MemberStatus;
  HistoryType = HistoryType;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private location: Location,
    private memberService: MemberService,
    private message: NzMessageService,
    private billingService: BillingService,
    private pdfService: PdfService,
    private authService: AuthService
  ) {
  }

  ngOnInit() {
    const memberId = this.route.snapshot.paramMap.get('id');
    if (memberId) {
      this.loadMember(memberId);
      this.loadPaymentHistory(memberId);
    } else {
      this.message.error('ID du membre manquant');
      this.router.navigate(['/members']);
    }
  }

  private loadMember(id: string) {
    this.loadingSignal.set(true);
    this.memberService.getMemberById(id).subscribe({
      next: (member) => {
        if (member) {
          this.memberSignal.set(member);
          this.loadMemberHistory();
        } else {
          this.message.error('Membre non trouvé');
          this.router.navigate(['/members']);
        }
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement du membre:', error);
        this.message.error('Erreur lors du chargement du membre');
        this.loadingSignal.set(false);
        this.router.navigate(['/members']);
      }
    });
  }

  private loadMemberHistory() {
    const member = this.member;
    if (!member) return;

    this.loadingHistorySignal.set(true);
    this.memberService.getMemberHistory(member.id).subscribe({
      next: (history) => {
        this.historySignal.set(history);
        this.loadingHistorySignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement de l\'historique:', error);
        this.loadingHistorySignal.set(false);
      }
    });
  }

  goBack() {
    this.location.back();
  }

  editMember() {
    const member = this.member;
    if (member) {
      this.router.navigate(['/members/edit', member.id]);
    }
  }

  // Méthodes utilitaires pour les couleurs et textes
  getTypeColor(type: MemberType): string {
    switch (type) {
      case MemberType.STUDENT:
        return 'blue';
      case MemberType.PROFESSIONAL:
        return 'green';
      default:
        return 'default';
    }
  }

  getTypeText(type: MemberType): string {
    switch (type) {
      case MemberType.STUDENT:
        return 'Étudiant';
      case MemberType.PROFESSIONAL:
        return 'Professionnel';
      case MemberType.COMPANY:
        return 'Entreprise';
      default:
        return 'Inconnu';
    }
  }

  getStatusColor(status: MemberStatus): string {
    switch (status) {
      case MemberStatus.ACTIVE:
        return 'success';
      case MemberStatus.INACTIVE:
        return 'default';
      default:
        return 'default';
    }
  }

  getStatusText(status: MemberStatus): string {
    switch (status) {
      case MemberStatus.ACTIVE:
        return 'Actif';
      case MemberStatus.INACTIVE:
        return 'Inactif';
      default:
        return 'Inconnu';
    }
  }

  getHistoryTypeText(type: HistoryType): string {
    switch (type) {
      case HistoryType.RESERVATION:
        return 'Réservation';
      case HistoryType.PAYMENT:
        return 'Paiement';
      case HistoryType.SUBSCRIPTION_CHANGE:
        return 'Changement d\'abonnement';
      case HistoryType.STATUS_CHANGE:
        return 'Changement de statut';
      default:
        return type;
    }
  }

  getHistoryTypeColor(type: HistoryType): string {
    switch (type) {
      case HistoryType.RESERVATION:
        return 'blue';
      case HistoryType.PAYMENT:
        return 'success';
      case HistoryType.SUBSCRIPTION_CHANGE:
        return 'processing';
      case HistoryType.STATUS_CHANGE:
        return 'warning';
      default:
        return 'default';
    }
  }

  // Méthodes pour l'historique des paiements
  private loadPaymentHistory(memberId: string) {
    this.billingService.getPaymentHistory(memberId).subscribe({
      next: (payments) => {
        this.paymentHistorySignal.set(payments);
      },
      error: (error) => {
        console.error('Erreur lors du chargement de l\'historique des paiements:', error);
      }
    });
  }

  getTotalPayments(): number {
    return this.paymentHistory.reduce((total, payment) => total + payment.amount, 0);
  }

  getLastPaymentDate(): Date | null {
    if (this.paymentHistory.length === 0) return null;
    return this.paymentHistory.sort((a, b) => b.date.getTime() - a.date.getTime())[0].date;
  }

  isRecentPayment(payment: PaymentHistory): boolean {
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    return payment.date > oneMonthAgo;
  }

  getFormattedDate(date: Date | string | undefined): string {
    if (!date) return '-';

    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      if (isNaN(dateObj.getTime())) return '-';

      return dateObj.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return '-';
    }
  }

  getFormattedDateTime(date: Date | string | undefined): string {
    if (!date) return '-';

    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      if (isNaN(dateObj.getTime())) return '-';

      return dateObj.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return '-';
    }
  }

  getPaymentMethodLabel(method: string): string {
    switch (method) {
      case 'card': return 'Carte bancaire';
      case 'transfer': return 'Virement';
      case 'cash': return 'Espèces';
      case 'check': return 'Chèque';
      default: return method;
    }
  }

  downloadPaymentReceipt(payment: PaymentHistory) {
    if (!this.member) return;

    // Récupérer les 3 derniers paiements pour le contexte
    const recentPayments = this.paymentHistory
      .sort((a, b) => b.date.getTime() - a.date.getTime())
      .slice(0, 3);

    // Générer le PDF
    this.pdfService.generatePaymentReceipt(payment, this.member, recentPayments);
    this.message.success('Reçu de paiement téléchargé');
  }

  // Méthodes pour vérifier les permissions selon les plans
  canAccessSubscriptions(): boolean {
    return this.authService.canAccessSubscriptions();
  }

  canAccessBilling(): boolean {
    return this.authService.hasAnyResourceRole(['STARTER_PLAN', 'PROFESSIONAL_PLAN', 'ENTREPRISE_PLAN']);
  }
}
