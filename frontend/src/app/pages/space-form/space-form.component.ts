import { Component, OnInit, signal, ChangeDetectorRef } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzTimePickerModule } from 'ng-zorro-antd/time-picker';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzEmptyModule } from 'ng-zorro-antd/empty';

import {
  Space,
  SpaceType,
  SpaceStatus,
  Floor,
  Equipment,
  EquipmentType,
  EquipmentStatus,
  CreateSpaceRequest,
  UpdateSpaceRequest
} from '../../models/space.model';
import { SpaceService } from '../../services/space.service';
import { EnumMapperService } from '../../services/enum-mapper.service';
import { ErrorService, ValidationMessages } from '../../services/error.service';

@Component({
  selector: 'app-space-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    NzCardModule,
    NzButtonModule,
    NzIconModule,
    NzInputModule,
    NzSelectModule,
    NzFormModule,
    NzInputNumberModule,
    NzSwitchModule,
    NzTimePickerModule,
    NzTagModule,
    NzDividerModule,
    NzSpinModule,
    NzGridModule,
    NzCheckboxModule,
    NzEmptyModule
  ],
  templateUrl: './space-form.component.html',
  styleUrl: './space-form.component.css'
})
export class SpaceFormComponent implements OnInit {
  // Signaux pour la réactivité
  private loadingSignal = signal<boolean>(false);
  private savingSignal = signal<boolean>(false);
  private spaceSignal = signal<Space | null>(null);

  // Getters pour les templates
  get loading() { return this.loadingSignal(); }
  get saving() { return this.savingSignal(); }
  get space() { return this.spaceSignal(); }

  // Formulaire
  spaceForm!: FormGroup;
  isEditMode = false;
  spaceId: string | null = null;

  // Enums pour les templates
  SpaceType = SpaceType;
  SpaceStatus = SpaceStatus;
  Floor = Floor;
  EquipmentType = EquipmentType;
  EquipmentStatus = EquipmentStatus;

  // Options pour les selects
  spaceTypeOptions: Array<{label: string, value: SpaceType}> = [];
  floorOptions: Array<{label: string, value: Floor}> = [];

  validation_messages: ValidationMessages = {
    'name': [
      { type: 'required', message: 'Le nom de l\'espace est obligatoire' },
      { type: 'minlength', message: 'Le nom doit contenir au moins 2 caractères' }
    ],
    'type': [
      { type: 'required', message: 'Le type d\'espace est obligatoire' }
    ],
    'capacity': [
      { type: 'required', message: 'La capacité est obligatoire' },
      { type: 'min', message: 'La capacité doit être d\'au moins 1 personne' },
      { type: 'max', message: 'La capacité ne peut pas dépasser 1000 personnes' }
    ],
    'hourlyRate': [
      { type: 'min', message: 'Le tarif horaire ne peut pas être négatif' }
    ],
    'dailyRate': [
      { type: 'min', message: 'Le tarif journalier ne peut pas être négatif' }
    ],
    'monthlyRate': [
      { type: 'min', message: 'Le tarif mensuel ne peut pas être négatif' }
    ]
  };

  equipmentTypeOptions = [
    { label: 'Ordinateur', value: EquipmentType.COMPUTER },
    { label: 'Écran', value: EquipmentType.MONITOR },
    { label: 'Imprimante', value: EquipmentType.PRINTER },
    { label: 'Projecteur', value: EquipmentType.PROJECTOR },
    { label: 'Tableau blanc', value: EquipmentType.WHITEBOARD },
    { label: 'Écran TV', value: EquipmentType.TV_SCREEN },
    { label: 'Téléphone', value: EquipmentType.PHONE },
    { label: 'Webcam', value: EquipmentType.WEBCAM },
    { label: 'Microphone', value: EquipmentType.MICROPHONE },
    { label: 'Haut-parleurs', value: EquipmentType.SPEAKERS },
    { label: 'Bureau', value: EquipmentType.DESK },
    { label: 'Chaise', value: EquipmentType.CHAIR },
    { label: 'Rangement', value: EquipmentType.STORAGE },
    { label: 'WiFi', value: EquipmentType.WIFI },
    { label: 'Ethernet', value: EquipmentType.ETHERNET }
  ];

  // Commodités prédéfinies
  availableAmenities = [
    'WiFi', 'Climatisation', 'Chauffage', 'Fenêtre', 'Lumière naturelle',
    'Prise électrique', 'Éclairage LED', 'Isolation phonique', 'Accès handicapé',
    'Parking', 'Cuisine', 'Machine à café', 'Réfrigérateur', 'Micro-ondes'
  ];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private location: Location,
    private spaceService: SpaceService,
    private enumMapper: EnumMapperService,
    private message: NzMessageService,
    private cdr: ChangeDetectorRef,
    private errorService: ErrorService
  ) {
    // Initialiser les options des selects
    this.spaceTypeOptions = this.enumMapper.getSpaceTypeOptions();
    this.floorOptions = this.enumMapper.getFloorOptions();

    this.initializeForm();
  }

  ngOnInit() {
    this.spaceId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.spaceId;

    if (this.isEditMode && this.spaceId) {
      this.loadSpace(this.spaceId);
    }
  }

  private initializeForm() {
    this.spaceForm = this.fb.group({
      // Informations de base - seul le nom est requis
      name: ['', [Validators.required, Validators.minLength(2)]],
      description: [''],
      type: [null],
      capacity: [null, [Validators.min(1)]],
      location: [''],
      floor: [Floor.GROUND_FLOOR], // Valeur par défaut : Rez-de-chaussée
      area: [null, [Validators.min(1)]],
      isActive: [true], // Champ isActive directement dans le formulaire

      // Équipements
      equipment: this.fb.array([]),

      // Commodités
      amenities: [[]],

      // Règles
      rules: this.fb.array([]),

      // Disponibilités
      availability: this.fb.group({
        isActive: [true],
        advanceBookingDays: [30, [Validators.min(1)]],
        minBookingDuration: [60, [Validators.min(15)]],
        maxBookingDuration: [480, [Validators.min(60)]],
        bufferTime: [15, [Validators.min(0)]],
        schedule: this.fb.group({
          monday: this.createDayScheduleGroup(),
          tuesday: this.createDayScheduleGroup(),
          wednesday: this.createDayScheduleGroup(),
          thursday: this.createDayScheduleGroup(),
          friday: this.createDayScheduleGroup(),
          saturday: this.createDayScheduleGroup(),
          sunday: this.createDayScheduleGroup()
        })
      }),

      // Tarification
      pricing: this.fb.group({
        hourlyRate: [null, [Validators.min(0)]],
        dailyRate: [null, [Validators.min(0)]],
        weeklyRate: [null, [Validators.min(0)]],
        monthlyRate: [null, [Validators.min(0)]]
      })
    });
  }

  private createDayScheduleGroup() {
    return this.fb.group({
      isOpen: [true],
      openTime: ['08:00'],
      closeTime: ['18:00'],
      breaks: this.fb.array([])
    });
  }

  // Convertir les heures des time-pickers (Date) en chaînes de caractères (HH:mm)
  private convertScheduleTimes(schedule: any): any {
    if (!schedule) return {};

    const convertedSchedule: any = {};
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

    days.forEach(day => {
      if (schedule[day]) {
        convertedSchedule[day] = {
          isOpen: schedule[day].isOpen,
          openTime: this.convertTimeToString(schedule[day].openTime),
          closeTime: this.convertTimeToString(schedule[day].closeTime),
          breaks: schedule[day].breaks || []
        };
      }
    });

    return convertedSchedule;
  }

  // Convertir un objet Date ou une chaîne en format HH:mm
  private convertTimeToString(time: any): string {
    if (!time) return '08:00';

    if (typeof time === 'string') {
      return time;
    }

    if (time instanceof Date) {
      const hours = time.getHours().toString().padStart(2, '0');
      const minutes = time.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    }

    return '08:00';
  }

  // Convertir une chaîne HH:mm en objet Date pour les time-pickers
  private convertStringToTime(timeString: string): Date {
    if (!timeString) return new Date(2000, 0, 1, 8, 0);

    const [hours, minutes] = timeString.split(':').map(Number);
    return new Date(2000, 0, 1, hours || 8, minutes || 0);
  }

  // Méthode helper pour obtenir le FormGroup d'un jour spécifique
  getDayFormGroup(day: string): FormGroup {
    return this.spaceForm.get('availability.schedule.' + day) as FormGroup;
  }

  private loadSpace(id: string) {
    this.loadingSignal.set(true);
    this.spaceService.getSpaceById(id).subscribe({
      next: (space) => {
        if (space) {
          this.spaceSignal.set(space);
          this.populateForm(space);
        } else {
          this.message.error('Espace non trouvé');
          this.goBack();
        }
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement:', error);
        this.message.error('Erreur lors du chargement de l\'espace');
        this.loadingSignal.set(false);
        this.goBack();
      }
    });
  }

  private populateForm(space: Space) {
    // Remplir les équipements
    const equipmentArray = this.spaceForm.get('equipment') as FormArray;
    equipmentArray.clear();
    if (space.equipment && Array.isArray(space.equipment)) {
      space.equipment.forEach(equipment => {
        equipmentArray.push(this.createEquipmentGroup(equipment));
      });
    }

    // Remplir les règles
    const rulesArray = this.spaceForm.get('rules') as FormArray;
    rulesArray.clear();
    if (space.rules && Array.isArray(space.rules)) {
      space.rules.forEach(rule => {
        rulesArray.push(this.fb.control(rule, [Validators.required]));
      });
    }

    // Remplir le formulaire
    const formData: any = {
      name: space.name,
      description: space.description,
      type: space.type,
      capacity: space.capacity,
      location: space.location,
      floor: space.floor,
      area: space.area,
      isActive: space.isActive !== undefined ? space.isActive : true,
      amenities: space.amenities,
    };

    // Ajouter la disponibilité si elle existe
    if (space.availability) {
      formData.availability = {
        isActive: space.availability.isActive,
        advanceBookingDays: space.availability.advanceBookingDays,
        minBookingDuration: space.availability.minBookingDuration,
        maxBookingDuration: space.availability.maxBookingDuration,
        bufferTime: space.availability.bufferTime,
        schedule: space.availability.schedule ? {
          monday: {
            isOpen: space.availability.schedule.monday?.isOpen || false,
            openTime: this.convertStringToTime(space.availability.schedule.monday?.openTime || '08:00'),
            closeTime: this.convertStringToTime(space.availability.schedule.monday?.closeTime || '18:00')
          },
          tuesday: {
            isOpen: space.availability.schedule.tuesday?.isOpen || false,
            openTime: this.convertStringToTime(space.availability.schedule.tuesday?.openTime || '08:00'),
            closeTime: this.convertStringToTime(space.availability.schedule.tuesday?.closeTime || '18:00')
          },
          wednesday: {
            isOpen: space.availability.schedule.wednesday?.isOpen || false,
            openTime: this.convertStringToTime(space.availability.schedule.wednesday?.openTime || '08:00'),
            closeTime: this.convertStringToTime(space.availability.schedule.wednesday?.closeTime || '18:00')
          },
          thursday: {
            isOpen: space.availability.schedule.thursday?.isOpen || false,
            openTime: this.convertStringToTime(space.availability.schedule.thursday?.openTime || '08:00'),
            closeTime: this.convertStringToTime(space.availability.schedule.thursday?.closeTime || '18:00')
          },
          friday: {
            isOpen: space.availability.schedule.friday?.isOpen || false,
            openTime: this.convertStringToTime(space.availability.schedule.friday?.openTime || '08:00'),
            closeTime: this.convertStringToTime(space.availability.schedule.friday?.closeTime || '18:00')
          },
          saturday: {
            isOpen: space.availability.schedule.saturday?.isOpen || false,
            openTime: this.convertStringToTime(space.availability.schedule.saturday?.openTime || '09:00'),
            closeTime: this.convertStringToTime(space.availability.schedule.saturday?.closeTime || '17:00')
          },
          sunday: {
            isOpen: space.availability.schedule.sunday?.isOpen || false,
            openTime: this.convertStringToTime(space.availability.schedule.sunday?.openTime || '09:00'),
            closeTime: this.convertStringToTime(space.availability.schedule.sunday?.closeTime || '17:00')
          }
        } : {}
      };
    }

    // Ajouter le pricing si il existe
    if (space.pricing) {
      formData.pricing = {
        hourlyRate: space.pricing.hourlyRate,
        dailyRate: space.pricing.dailyRate,
        weeklyRate: space.pricing.weeklyRate,
        monthlyRate: space.pricing.monthlyRate,
        currency: space.pricing.currency
      };
    }

    this.spaceForm.patchValue(formData);
  }

  // Getters pour les FormArrays
  get equipmentArray() {
    return this.spaceForm.get('equipment') as FormArray;
  }

  get rulesArray() {
    return this.spaceForm.get('rules') as FormArray;
  }

  // Gestion des équipements
  createEquipmentGroup(equipment?: Equipment) {
    return this.fb.group({
      id: [equipment?.id || Date.now().toString()],
      name: [equipment?.name || '', [Validators.required]],
      type: [equipment?.type || null, [Validators.required]],
      brand: [equipment?.brand || ''],
      model: [equipment?.model || ''],
      quantity: [equipment?.quantity || 1, [Validators.required, Validators.min(1)]],
      status: [equipment?.status || EquipmentStatus.WORKING, [Validators.required]],
      description: [equipment?.description || '']
    });
  }

  addEquipment() {
    this.equipmentArray.push(this.createEquipmentGroup());
  }

  removeEquipment(index: number) {
    this.equipmentArray.removeAt(index);
  }

  // Gestion des règles
  addRule() {
    this.rulesArray.push(this.fb.control('', [Validators.required]));
  }

  removeRule(index: number) {
    this.rulesArray.removeAt(index);
  }

  // Gestion des changements de checkbox
  onDayCheckboxChange(day: string) {
    // Forcer la détection des changements pour mettre à jour l'affichage
    this.cdr.detectChanges();
  }

  // Basculer l'état actif de l'espace
  toggleSpaceActive() {
    const currentValue = this.spaceForm.get('isActive')?.value;
    this.spaceForm.get('isActive')?.setValue(!currentValue);
  }

  // Getter pour l'état actif
  get isSpaceActive(): boolean {
    return this.spaceForm.get('isActive')?.value || false;
  }

  // Obtenir le texte du statut
  getStatusText(status: SpaceStatus): string {
    switch (status) {
      case SpaceStatus.AVAILABLE:
        return 'Disponible';
      case SpaceStatus.OCCUPIED:
        return 'Occupé';
      case SpaceStatus.SATURATED:
        return 'Saturé';
      case SpaceStatus.MAINTENANCE:
        return 'En maintenance';
      case SpaceStatus.OUT_OF_ORDER:
        return 'Hors service';
      case SpaceStatus.RESERVED:
        return 'Réservé';
      case SpaceStatus.UNAVAILABLE:
        return 'Indisponible';
      default:
        return 'Inconnu';
    }
  }

  // Navigation
  goBack() {
    this.location.back();
  }

  // Soumission du formulaire
  onSubmit() {
    if (this.spaceForm.valid) {
      this.savingSignal.set(true);
      const formValue = this.spaceForm.value;

      if (this.isEditMode && this.spaceId) {
        this.updateSpace(formValue);
      } else {
        this.createSpace(formValue);
      }
    } else {
      const validationMessage = this.errorService.getFormValidationMessage(this.spaceForm);
      this.message.warning(validationMessage);
      this.markFormGroupTouched(this.spaceForm);
    }
  }

  private createSpace(formValue: any) {
    const request: CreateSpaceRequest = {
      name: formValue.name,
      description: formValue.description,
      type: formValue.type,
      capacity: formValue.capacity,
      location: formValue.location,
      floor: formValue.floor,
      area: formValue.area,
      equipment: formValue.equipment,
      amenities: formValue.amenities,
      availability: {
        ...formValue.availability,
        schedule: this.convertScheduleTimes(formValue.availability?.schedule),
        exceptions: []
      },
      pricing: {
        ...formValue.pricing,
        discounts: []
      },
      rules: formValue.rules
    };

    this.spaceService.createSpace(request).subscribe({
      next: (space) => {
        this.message.success('Espace créé avec succès');
        this.router.navigate(['/spaces/details', space.id]);
        this.savingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors de la création:', error);
        this.message.error('Erreur lors de la création de l\'espace');
        this.savingSignal.set(false);
      }
    });
  }

  private updateSpace(formValue: any) {
    if (!this.spaceId) return;

    const request: UpdateSpaceRequest = {
      id: this.spaceId,
      name: formValue.name,
      description: formValue.description,
      type: formValue.type,
      capacity: formValue.capacity,
      location: formValue.location,
      floor: formValue.floor,
      area: formValue.area,
      equipment: formValue.equipment,
      amenities: formValue.amenities,
      availability: {
        ...formValue.availability,
        schedule: this.convertScheduleTimes(formValue.availability?.schedule),
        exceptions: this.space?.availability.exceptions || []
      },
      pricing: {
        ...formValue.pricing,
        discounts: this.space?.pricing.discounts || []
      },
      rules: formValue.rules
    };

    this.spaceService.updateSpace(request).subscribe({
      next: (space) => {
        this.message.success('Espace modifié avec succès');
        this.router.navigate(['/spaces/details', space.id]);
        this.savingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors de la modification:', error);
        this.message.error('Erreur lors de la modification de l\'espace');
        this.savingSignal.set(false);
      }
    });
  }



  // Méthodes utilitaires pour les templates
  getEquipmentTypeLabel(type: EquipmentType): string {
    const option = this.equipmentTypeOptions.find(opt => opt.value === type);
    return option ? option.label : type;
  }

  getDayLabel(day: string): string {
    const dayLabels: { [key: string]: string } = {
      'monday': 'Lundi',
      'tuesday': 'Mardi',
      'wednesday': 'Mercredi',
      'thursday': 'Jeudi',
      'friday': 'Vendredi',
      'saturday': 'Samedi',
      'sunday': 'Dimanche'
    };
    return dayLabels[day] || day;
  }

  // Méthodes utilitaires pour la validation
  isFieldInvalid(fieldName: string): boolean {
    return this.errorService.isFieldInvalid(fieldName, this.spaceForm);
  }

  getErrorMessage(controlName: string): string {
    return this.errorService.getErrorMessage(controlName, this.spaceForm, this.validation_messages);
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    this.errorService.markFormGroupTouched(formGroup);
  }
}
