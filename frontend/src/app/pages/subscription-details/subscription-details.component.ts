import { Component, OnInit, signal } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzListModule } from 'ng-zorro-antd/list';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';

import { NzMessageService } from 'ng-zorro-antd/message';
import { NzDescriptionsModule } from 'ng-zorro-antd/descriptions';

import { SubscriptionPlan, SubscriptionType, MembershipType } from '../../models/subscription.model';
import { SubscriptionService } from '../../services/subscription.service';

@Component({
  selector: 'app-subscription-details',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzTagModule,
    NzButtonModule,
    NzIconModule,
    NzDividerModule,
    NzListModule,
    NzSpinModule,
    NzEmptyModule,
    NzToolTipModule,

    NzDescriptionsModule
  ],
  templateUrl: './subscription-details.component.html',
  styleUrl: './subscription-details.component.css'
})
export class SubscriptionDetailsComponent implements OnInit {
  // Signaux pour la réactivité
  private planSignal = signal<SubscriptionPlan | null>(null);
  private loadingSignal = signal<boolean>(false);

  // Getters pour les templates
  get plan() {
    return this.planSignal();
  }

  get loading() {
    return this.loadingSignal();
  }

  // Enums pour les templates
  SubscriptionType = SubscriptionType;
  MembershipType = MembershipType;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private location: Location,
    private subscriptionService: SubscriptionService,
    private message: NzMessageService
  ) {
  }

  ngOnInit() {
    const planId = this.route.snapshot.paramMap.get('id');
    if (planId) {
      this.loadPlan(planId);
    } else {
      this.message.error('ID du plan manquant');
      this.router.navigate(['/subscriptions']);
    }
  }

  private loadPlan(id: string) {
    this.loadingSignal.set(true);
    this.subscriptionService.getPlanById(id).subscribe({
      next: (plan) => {
        if (plan) {
          this.planSignal.set(plan);
        } else {
          this.message.error('Plan d\'abonnement non trouvé');
          this.router.navigate(['/subscriptions']);
        }
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement du plan:', error);
        this.message.error('Erreur lors du chargement du plan');
        this.loadingSignal.set(false);
        this.router.navigate(['/subscriptions']);
      }
    });
  }

  goBack() {
    this.location.back();
  }

  editPlan() {
    const plan = this.plan;
    if (plan) {
      this.router.navigate(['/subscriptions/edit', plan.id]);
    }
  }

  // Méthodes utilitaires pour les couleurs et textes
  getTypeColor(type: SubscriptionType): string {
    switch (type) {
      case SubscriptionType.DAILY:
        return 'blue';
      case SubscriptionType.WEEKLY:
        return 'cyan';
      case SubscriptionType.MONTHLY:
        return 'green';
      case SubscriptionType.FLEXIBLE:
        return 'purple';
      default:
        return 'default';
    }
  }

  getTypeLabel(type: SubscriptionType): string {
    switch (type) {
      case SubscriptionType.DAILY:
        return 'Journalier';
      case SubscriptionType.WEEKLY:
        return 'Hebdomadaire';
      case SubscriptionType.MONTHLY:
        return 'Mensuel';
      case SubscriptionType.HALF_YEARLY:
        return 'Semestriel';
      case SubscriptionType.YEARLY:
        return 'Annuel';
      case SubscriptionType.FLEXIBLE:
        return 'Flexible';
      default:
        return 'Inconnu';
    }
  }

  getMembershipColor(type: MembershipType): string {
    switch (type) {
      case MembershipType.STUDENT:
        return 'blue';
      case MembershipType.PROFESSIONAL:
        return 'green';
      case MembershipType.COMPANY:
        return 'gold';
      default:
        return 'default';
    }
  }

  getMembershipLabel(type: MembershipType): string {
    switch (type) {
      case MembershipType.STUDENT:
        return 'Étudiant';
      case MembershipType.PROFESSIONAL:
        return 'Professionnel';
      case MembershipType.COMPANY:
        return 'Premium';
      default:
        return 'Inconnu';
    }
  }

  getDurationText(plan: SubscriptionPlan): string {
    if (plan.type === SubscriptionType.FLEXIBLE) {
      return `${plan.duration} jour${plan.duration > 1 ? 's' : ''}`;
    }

    switch (plan.type) {
      case SubscriptionType.DAILY:
        return '1 jour';
      case SubscriptionType.WEEKLY:
        return '7 jours';
      case SubscriptionType.MONTHLY:
        return '30 jours';
      case SubscriptionType.HALF_YEARLY:
        return '6 mois';
      case SubscriptionType.YEARLY:
        return '1 an';
      default:
        return `${plan.duration} jour${plan.duration > 1 ? 's' : ''}`;
    }
  }

  getDaysText(days: string[]): string {
    const dayNames: { [key: string]: string } = {
      'monday': 'Lundi',
      'tuesday': 'Mardi',
      'wednesday': 'Mercredi',
      'thursday': 'Jeudi',
      'friday': 'Vendredi',
      'saturday': 'Samedi',
      'sunday': 'Dimanche'
    };

    return days.map(day => dayNames[day] || day).join(', ');
  }
}
