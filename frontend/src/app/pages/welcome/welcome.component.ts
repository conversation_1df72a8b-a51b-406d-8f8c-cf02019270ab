import { Component, OnInit, OnDestroy, signal } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { forkJoin, Subject, takeUntil } from 'rxjs';

import { UiPermissionsService } from '../../services/ui-permissions.service';
import { SiteContextService } from '../../services/site-context.service';
import { DashboardService, DashboardStats } from '../../services/dashboard.service';
import { DailyCalendarComponent } from '../../components/daily-calendar/daily-calendar.component';

@Component({
  selector: 'app-welcome',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    NzCardModule,
    NzButtonModule,
    NzIconModule,
    NzSpinModule,
    DailyCalendarComponent
  ],
  templateUrl: './welcome.component.html',
  styleUrl: './welcome.component.css'
})
export class WelcomeComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  // Signaux pour la gestion d'état
  private statsSignal = signal<DashboardStats>({
    totalMembers: 0,
    activeMembers: 0,
    totalSpaces: 0,
    availableSpaces: 0,
    totalReservations: 0,
    activeReservations: 0,
    monthlyRevenue: 0,
    totalRevenue: 0,
    pendingPayments: 0,
    overduePayments: 0,
    activeSubscriptions: 0,
    expiringSubscriptions: 0,
    occupancyRate: 0
  });
  private loadingSignal = signal<boolean>(false);

  // Getters pour les templates
  get stats() { return this.statsSignal(); }
  get loading() { return this.loadingSignal(); }



  constructor(
    private router: Router,
    private dashboardService: DashboardService,
    private uiPermissions: UiPermissionsService,
    private siteContext: SiteContextService
  ) {}

  ngOnInit() {
    console.log('🏠 Welcome component initialized');

    // Charger le site actuel si pas encore disponible, puis charger les stats
    this.initializeSiteAndStats();
  }

  private async initializeSiteAndStats() {
    try {
      // Vérifier si le site est déjà disponible
      let currentSite = this.siteContext.getCurrentSite();

      if (!currentSite) {
        console.log('🔄 No site in context, loading current site...');
        // Charger le site actuel depuis le backend
        currentSite = await this.siteContext.loadCurrentSite();
      }

      if (currentSite) {
        console.log('✅ Site available, loading dashboard stats for:', currentSite.name);
        this.loadDashboardStats();
      } else {
        console.log('⚠️ No site available after loading attempt');
        this.resetStatsToZero();
      }

      // Écouter les changements de site pour les futures mises à jour
      this.siteContext.currentSite$
        .pipe(takeUntil(this.destroy$))
        .subscribe(site => {
          if (site && site !== currentSite) {
            console.log('🔄 Site changed, reloading dashboard stats for:', site.name);
            this.loadDashboardStats();
            currentSite = site;
          }
        });
    } catch (error) {
      console.error('Error initializing site and stats:', error);
      this.resetStatsToZero();
    }
  }

  private resetStatsToZero() {
    this.statsSignal.set({
      totalMembers: 0,
      activeMembers: 0,
      totalSpaces: 0,
      availableSpaces: 0,
      totalReservations: 0,
      activeReservations: 0,
      monthlyRevenue: 0,
      totalRevenue: 0,
      pendingPayments: 0,
      overduePayments: 0,
      activeSubscriptions: 0,
      expiringSubscriptions: 0,
      occupancyRate: 0
    });
    this.loadingSignal.set(false);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadDashboardStats() {
    this.loadingSignal.set(true);
    const currentSite = this.siteContext.getCurrentSite();
    console.log('Starting to load dashboard stats for site:', currentSite?.name || 'No site selected');

    if (!currentSite) {
      this.loadingSignal.set(false);
      return;
    }

    // Utiliser le nouveau service Dashboard pour récupérer toutes les statistiques
    this.dashboardService.getDashboardStats(currentSite.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (stats) => {
          console.log('Dashboard stats loaded from backend:', stats);

          // Adapter les statistiques selon les permissions
          const adaptedStats: DashboardStats = {
            ...stats,
            monthlyRevenue: this.uiPermissions.canShowBillingInfo() ? (stats.monthlyRevenue || 0) : -1,
            totalRevenue: this.uiPermissions.canShowBillingInfo() ? (stats.totalRevenue || 0) : -1,
            pendingPayments: this.uiPermissions.canShowBillingInfo() ? (stats.pendingPayments || 0) : -1,
            overduePayments: this.uiPermissions.canShowBillingInfo() ? (stats.overduePayments || 0) : -1,
            activeSubscriptions: this.uiPermissions.canShowBillingInfo() ? (stats.activeSubscriptions || 0) : -1
          };

          this.statsSignal.set(adaptedStats);
          this.loadingSignal.set(false);
        },
        error: (error) => {
          console.error('Error loading dashboard stats:', error);
          this.resetStatsToZero();
        }
      });
  }



  // Méthodes de navigation pour les actions rapides
  navigateToNewMember(): void {
    this.router.navigate(['/members/new']);
  }

  navigateToNewReservation(): void {
    this.router.navigate(['/reservation-form']);
  }

  navigateToNewSpace(): void {
    this.router.navigate(['/spaces/new']);
  }

  navigateToNewBilling(): void {
    this.router.navigate(['/billing/new']);
  }
}
