<div class="welcome-container">
  <!-- Page Header -->
  <div class="page-header">
    <h1 class="page-title">
      <nz-icon nzType="dashboard" nzTheme="outline"></nz-icon>
      Tableau de bord
    </h1>
    <p class="page-subtitle">Vue d'ensemble de votre espace de coworking</p>
  </div>

  <!-- Stats Cards -->
  <div class="stats-grid" *ngIf="!loading; else loadingTemplate">
    <!-- Membres actifs -->
    <nz-card class="stat-card" nzHoverable routerLink="/members">
      <div class="stat-content">
        <div class="stat-icon members">
          <nz-icon nzType="team" nzTheme="outline"></nz-icon>
        </div>
        <div class="stat-info">
          <h3>{{ stats.activeMembers }}</h3>
          <p>Membres actifs</p>
          <small class="stat-detail">{{ stats.totalMembers }} au total</small>
        </div>
      </div>
    </nz-card>

    <!-- Espaces disponibles -->
    <nz-card class="stat-card" nzHoverable routerLink="/spaces">
      <div class="stat-content">
        <div class="stat-icon spaces">
          <nz-icon nzType="home" nzTheme="outline"></nz-icon>
        </div>
        <div class="stat-info">
          <h3>{{ stats.availableSpaces }}</h3>
          <p>Espaces disponibles</p>
          <small class="stat-detail">{{ stats.totalSpaces }} au total</small>
        </div>
      </div>
    </nz-card>

    <!-- Réservations à venir -->
    <nz-card class="stat-card" nzHoverable routerLink="/reservations">
      <div class="stat-content">
        <div class="stat-icon bookings">
          <nz-icon nzType="calendar" nzTheme="outline"></nz-icon>
        </div>
        <div class="stat-info">
          <h3>{{ stats.activeReservations }}</h3>
          <p>Réservations à venir</p>
          <small class="stat-detail">{{ stats.totalReservations }} au total</small>
        </div>
      </div>
    </nz-card>

    <!-- Revenus ce mois -->
    <nz-card class="stat-card" nzHoverable routerLink="/billing">
      <div class="stat-content">
        <div class="stat-icon revenue">
          <nz-icon nzType="dollar" nzTheme="outline"></nz-icon>
        </div>
        <div class="stat-info">
          <h3>{{ stats.monthlyRevenue === -1 ? '-' : (stats.monthlyRevenue | number:'1.0-0') + ' MAD' }}</h3>
          <p>Revenus ce mois</p>
          <small [class]="stats.totalRevenue === -1 ? 'stat-detail premium-feature' : 'stat-detail'">{{ stats.totalRevenue === -1 ? 'Fonctionnalité STARTER+' : (stats.totalRevenue | number:'1.0-0') + ' MAD au total' }}</small>
        </div>
      </div>
    </nz-card>

    <!-- Abonnements actifs -->
    <nz-card class="stat-card" nzHoverable routerLink="/subscriptions">
      <div class="stat-content">
        <div class="stat-icon subscriptions">
          <nz-icon nzType="credit-card" nzTheme="outline"></nz-icon>
        </div>
        <div class="stat-info">
          <h3>{{ stats.activeSubscriptions === -1 ? '-' : stats.activeSubscriptions }}</h3>
          <p>Abonnements actifs</p>
          <small class="stat-detail" *ngIf="stats.expiringSubscriptions && stats.expiringSubscriptions > 0 && stats.activeSubscriptions !== -1" style="color: #faad14;">
            {{ stats.expiringSubscriptions }} expirent bientôt
          </small>
          <small class="stat-detail" *ngIf="stats.activeSubscriptions === -1" style="color: #1890ff;">
            Fonctionnalité STARTER+
          </small>
        </div>
      </div>
    </nz-card>

    <!-- Paiements en attente -->
    <nz-card class="stat-card" nzHoverable routerLink="/billing">
      <div class="stat-content">
        <div class="stat-icon pending">
          <nz-icon nzType="clock-circle" nzTheme="outline"></nz-icon>
        </div>
        <div class="stat-info">
          <h3>{{ stats.pendingPayments === -1 ? '-' : (stats.pendingPayments | number:'1.0-0') + ' MAD' }}</h3>
          <p>Paiements en attente</p>
          <small class="stat-detail" *ngIf="stats.overduePayments && stats.overduePayments > 0 && stats.overduePayments !== -1" style="color: #ff4d4f;">
            {{ stats.overduePayments | number:'1.0-0' }} MAD en retard
          </small>
          <small class="stat-detail" *ngIf="stats.pendingPayments === -1" style="color: #1890ff;">
            Fonctionnalité STARTER+
          </small>
        </div>
      </div>
    </nz-card>

    <!-- Taux d'occupation -->
    <nz-card class="stat-card" nzHoverable routerLink="/spaces">
      <div class="stat-content">
        <div class="stat-icon occupancy">
          <nz-icon nzType="pie-chart" nzTheme="outline"></nz-icon>
        </div>
        <div class="stat-info">
          <h3>{{ (stats.occupancyRate | number:'1.2-2') }}%</h3>
          <p>Taux d'occupation</p>
          <small class="stat-detail">Basé sur la capacité totale</small>
        </div>
      </div>
    </nz-card>

    <!-- Nouveaux membres ce mois -->
    <nz-card class="stat-card" nzHoverable routerLink="/members">
      <div class="stat-content">
        <div class="stat-icon new-members">
          <nz-icon nzType="user-add" nzTheme="outline"></nz-icon>
        </div>
        <div class="stat-info">
          <h3>{{ stats.activeMembers || 0 }}</h3>
          <p>Membres actifs</p>
          <small class="stat-detail">{{ stats.totalMembers }} membres au total</small>
        </div>
      </div>
    </nz-card>
  </div>

  <!-- Template de chargement -->
  <ng-template #loadingTemplate>
    <div class="stats-grid">
      <nz-card class="stat-card" *ngFor="let item of [1,2,3,4,5,6,7,8]">
        <div class="stat-content">
          <div class="stat-icon">
            <nz-spin nzSimple></nz-spin>
          </div>
          <div class="stat-info">
            <h3>---</h3>
            <p>Chargement...</p>
          </div>
        </div>
      </nz-card>
    </div>
  </ng-template>


  <!-- Daily Calendar -->
  <div class="daily-calendar-section" *ngIf="!loading">
    <app-daily-calendar></app-daily-calendar>
  </div>

</div>
