.welcome-container {
  padding: 0;
  max-width: 1400px;
  margin: 0 auto;
}

/* Page Header */
.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0 0 8px 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title nz-icon {
  color: #6E56CF;
  font-size: 28px;
}

.page-subtitle {
  font-size: 16px;
  color: #8E8E93;
  margin: 0;
  font-weight: 400;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 48px;
}

.stat-card {
  border-radius: 12px !important;
  border: 1px solid #E5E5EA !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  transition: all 0.2s ease;
  background: #FFFFFF !important;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 8px;
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
}

.stat-icon.members {
  background-color: #EDE9F8;
  color: #6E56CF;
}

.stat-icon.spaces {
  background-color: #E8F5E8;
  color: #34C759;
}

.stat-icon.bookings {
  background-color: #E3F2FD;
  color: #007AFF;
}

.stat-icon.revenue {
  background-color: #FFF3E0;
  color: #FF9500;
}

.stat-icon.subscriptions {
  background-color: #F3E8FF;
  color: #722ed1;
}

.stat-icon.pending {
  background-color: #FFF7E6;
  color: #faad14;
}

.stat-icon.occupancy {
  background-color: #E6F7FF;
  color: #1890ff;
}

.stat-icon.new-members {
  background-color: #F6FFED;
  color: #52c41a;
}

.stat-info h3 {
  font-size: 28px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0 0 4px 0;
}

.stat-info p {
  font-size: 14px;
  color: #8E8E93;
  margin: 0;
  font-weight: 500;
}

.stat-detail {
  font-size: 12px;
  color: #8E8E93;
  margin-top: 4px;
  display: block;
}

.stat-detail.premium-feature {
  color: #1890ff; /* Bleu ciel */
  font-weight: 500;
}

/* Daily Calendar Section */
.daily-calendar-section {
  margin-top: 32px;
  margin-bottom: 32px;
}

.action-btn:hover {
  transform: translateY(-1px);
}

/* Primary button styling */
.action-btn.ant-btn-primary {
  background-color: #6E56CF !important;
  border-color: #6E56CF !important;
}

.action-btn.ant-btn-primary:hover {
  background-color: #5A47B8 !important;
  border-color: #5A47B8 !important;
}

/* Default button styling */
.action-btn.ant-btn-default {
  background-color: #FFFFFF !important;
  border-color: #E5E5EA !important;
  color: #1C1C1E !important;
}

.action-btn.ant-btn-default:hover {
  background-color: #EDE9F8 !important;
  border-color: #6E56CF !important;
  color: #6E56CF !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .welcome-container {
    padding: 16px 8px;
  }

  .welcome-title {
    font-size: 28px;
  }

  .welcome-subtitle {
    font-size: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }

  .stat-content {
    padding: 4px;
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .stat-info h3 {
    font-size: 24px;
  }
}

/* Section du calendrier quotidien */
.daily-calendar-section {
  margin-top: 32px;
}

/* Section des graphiques financiers */
.charts-section {
  margin-top: 32px;
}
