<div class="spaces-container">
  <!-- En-tête -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <nz-icon nzType="environment" nzTheme="outline"></nz-icon>
        Gestion des espaces
      </h1>
      <p class="page-description">Gérez vos espaces de travail, salles de réunion et équipements</p>
    </div>
    <div class="header-actions">
      <button nz-button nzType="default" nzSize="large" (click)="navigateToCalendar()" class="calendar-button">
        <nz-icon nzType="calendar" nzTheme="outline"></nz-icon>
        Planning
      </button>
      <button nz-button nzType="primary" nzSize="large" (click)="navigateToCreate()" class="add-button">
        <nz-icon nzType="plus"></nz-icon>
        Nouvel espace
      </button>
    </div>
  </div>

  <!-- Statistiques rapides -->
  <div class="stats-section">
    <div nz-row [nzGutter]="[16, 16]">
      <div nz-col nzXs="12" nzSm="12" nzMd="6" nzLg="6" nzXl="6">
        <nz-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total-spaces">
              <nz-icon nzType="home" nzTheme="outline"></nz-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">Total espaces</div>
              <div class="stat-value">{{ getTotalSpaces() }}</div>
            </div>
          </div>
        </nz-card>
      </div>
      <div nz-col nzXs="12" nzSm="12" nzMd="6" nzLg="6" nzXl="6">
        <nz-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon available-spaces">
              <nz-icon nzType="check-circle" nzTheme="outline"></nz-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">Disponibles</div>
              <div class="stat-value available">{{ getAvailableSpacesCount() }}</div>
            </div>
          </div>
        </nz-card>
      </div>
      <div nz-col nzXs="12" nzSm="12" nzMd="6" nzLg="6" nzXl="6">
        <nz-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon occupied-spaces">
              <nz-icon nzType="close-circle" nzTheme="outline"></nz-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">Occupés</div>
              <div class="stat-value occupied">{{ getOccupiedSpacesCount() }}</div>
            </div>
          </div>
        </nz-card>
      </div>
      <div nz-col nzXs="12" nzSm="12" nzMd="6" nzLg="6" nzXl="6">
        <nz-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total-capacity">
              <nz-icon nzType="team" nzTheme="outline"></nz-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">Capacité totale</div>
              <div class="stat-value">{{ getTotalCapacity() }}</div>
            </div>
          </div>
        </nz-card>
      </div>
    </div>
  </div>

  <!-- Filtres et recherche -->
  <nz-card class="filters-card" nzTitle="Filtres">
    <div class="filters-row">
      <div class="filter-item">
        <label>Rechercher</label>
        <nz-input-group nzPrefixIcon="search">
          <input
            nz-input
            [formControl]="searchControl"
            placeholder="Nom ou description..."
          />
        </nz-input-group>
      </div>

      <div class="filter-item">
        <label>Type d'espace</label>
        <nz-select
          [(ngModel)]="selectedType"
          nzPlaceHolder="Tous les types"
          nzAllowClear
          (ngModelChange)="onTypeChange()"
        >
          <nz-option
            *ngFor="let option of spaceTypeOptions"
            [nzLabel]="option.label"
            [nzValue]="option.value"
          ></nz-option>
        </nz-select>
      </div>

      <div class="filter-item">
        <label>Localisation</label>
        <nz-input-group nzPrefixIcon="environment">
          <input
            nz-input
            [formControl]="locationControl"
            placeholder="Zone, bâtiment..."
          />
        </nz-input-group>
      </div>

      <div class="filter-item">
        <label>Étage</label>
        <nz-select
          [(ngModel)]="selectedFloor"
          nzPlaceHolder="Tous les étages"
          nzAllowClear
          (ngModelChange)="onFloorChange()"
        >
          <nz-option
            *ngFor="let option of floorOptions"
            [nzLabel]="option.label"
            [nzValue]="option.value"
          ></nz-option>
        </nz-select>
      </div>

      <div class="filter-item">
        <label>Capacité min.</label>
        <nz-input-number
          [(ngModel)]="minCapacity"
          [nzMin]="1"
          nzPlaceHolder="1"
          style="width: 100%"
          (ngModelChange)="onCapacityChange()"
        ></nz-input-number>
      </div>

      <div class="filter-actions">
        <button nz-button nzType="default" (click)="clearFilters()">
          <nz-icon nzType="clear"></nz-icon>
          Effacer les filtres
        </button>
      </div>
    </div>
  </nz-card>

  <!-- Liste des espaces -->
  <div class="spaces-content">
    <nz-spin [nzSpinning]="loading" nzTip="Chargement des espaces...">
      <div class="spaces-grid" *ngIf="spaces.length > 0; else noSpaces">
        <div class="space-card" *ngFor="let space of spaces">
          <nz-card class="space-item" [nzActions]="[actionEdit, actionDelete]">
            <!-- En-tête de la carte -->
            <div class="space-header">
              <div class="space-icon">
                <nz-icon [nzType]="getSpaceIcon(space.type)" nzTheme="outline"></nz-icon>
              </div>
              <div class="space-info">
                <h3 class="space-name" (click)="navigateToDetails(space)">
                  {{ space.name }}
                </h3>
                <p class="space-location">
                  <nz-icon nzType="environment" nzTheme="outline"></nz-icon>
                  {{ enumMapper.formatDisplayValue(space.location, 'Non spécifié') }}
                  <span *ngIf="space.floor"> - {{ enumMapper.getFloorDisplayName(space.floor) }}</span>
                </p>
              </div>
              <div class="space-status">
                <nz-tag [nzColor]="getStatusColor(space)">
                  {{ getStatusText(space) }}
                </nz-tag>
              </div>
            </div>

            <!-- Contenu de la carte -->
            <div class="space-content">
              <p class="space-description">{{ space.description }}</p>

              <div class="space-details">
                <div class="detail-item">
                  <nz-icon nzType="team" nzTheme="outline"></nz-icon>
                  <span>{{ space.capacity ? space.capacity + ' personne(s)' : 'Non spécifié' }}</span>
                </div>
                <div class="detail-item">
                  <nz-icon nzType="expand" nzTheme="outline"></nz-icon>
                  <span>{{ space.area ? space.area + ' m²' : 'Non spécifié' }}</span>
                </div>
                <div class="detail-item">
                  <nz-icon nzType="euro" nzTheme="outline"></nz-icon>
                  <span>{{ space.pricing.hourlyRate ? space.pricing.hourlyRate + ' MAD/h' : 'Non spécifié' }}</span>
                </div>
              </div>

              <div class="space-type">
                <nz-tag [nzColor]="getTypeColor(space.type)">
                  {{ enumMapper.getSpaceTypeDisplayName(space.type) }}
                </nz-tag>
              </div>

              <div class="space-equipment" *ngIf="space.equipment.length > 0">
                <h4>Équipements</h4>
                <div class="equipment-list">
                  <nz-tag
                    *ngFor="let equipment of getDisplayedEquipment(space.equipment)"
                    nzColor="blue"
                    class="equipment-tag"
                  >
                    {{ equipment.name }}
                  </nz-tag>
                  <nz-tag
                    *ngIf="hasMoreEquipment(space.equipment)"
                    nzColor="default"
                    class="equipment-more"
                  >
                    +{{ getRemainingEquipmentCount(space.equipment) }} autres
                  </nz-tag>
                </div>
              </div>

              <div class="space-amenities" *ngIf="space.amenities.length > 0">
                <h4>Commodités</h4>
                <div class="amenities-list">
                  <span
                    *ngFor="let amenity of getDisplayedAmenities(space.amenities)"
                    class="amenity-item"
                  >
                    {{ amenity }}
                  </span>
                  <span
                    *ngIf="hasMoreAmenities(space.amenities)"
                    class="amenities-more"
                  >
                    +{{ getRemainingAmenitiesCount(space.amenities) }} autres
                  </span>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <ng-template #actionEdit>
              <nz-icon nzType="edit" nzTheme="outline" (click)="navigateToEdit(space)"></nz-icon>
            </ng-template>
            <ng-template #actionDelete>
              <nz-icon
                nzType="delete"
                nzTheme="outline"
                (click)="confirmDeleteSpace(space)"
              ></nz-icon>
            </ng-template>
          </nz-card>
        </div>
      </div>

      <!-- État vide -->
      <ng-template #noSpaces>
        <nz-empty
          nzNotFoundImage="simple"
          nzNotFoundContent="Aucun espace trouvé"
        >
          <div nz-empty-footer>
            <button nz-button nzType="primary" (click)="navigateToCreate()">
              <nz-icon nzType="plus" nzTheme="outline"></nz-icon>
              <span>Créer le premier espace</span>
            </button>
          </div>
        </nz-empty>
      </ng-template>
    </nz-spin>
  </div>
</div>
