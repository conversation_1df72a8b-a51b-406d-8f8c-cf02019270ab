import { Component, OnInit, OnDestroy, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { FormsModule, FormControl, ReactiveFormsModule } from '@angular/forms';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzStatisticModule } from 'ng-zorro-antd/statistic';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzMessageModule } from 'ng-zorro-antd/message';

import { Space, SpaceType, SpaceStatus, SpaceSearchFilters, Floor } from '../../models/space.model';
import { SpaceService } from '../../services/space.service';
import { SiteContextService } from '../../services/site-context.service';
import { EnumMapperService } from '../../services/enum-mapper.service';
import {NzInputNumberComponent} from "ng-zorro-antd/input-number";

@Component({
  selector: 'app-spaces',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NzCardModule,
    NzButtonModule,
    NzIconModule,
    NzInputModule,
    NzSelectModule,
    NzTagModule,
    NzSpinModule,
    NzEmptyModule,
    NzGridModule,
    NzStatisticModule,
    NzDividerModule,
    NzDropDownModule,
    NzInputNumberComponent,
    NzModalModule,
    NzMessageModule
  ],
  templateUrl: './spaces.component.html',
  styleUrl: './spaces.component.css'
})
export class SpacesComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  // Signaux pour la réactivité
  private spacesSignal = signal<Space[]>([]);
  private loadingSignal = signal<boolean>(false);
  private searchFiltersSignal = signal<SpaceSearchFilters>({});

  // Getters pour les templates
  get spaces() { return this.spacesSignal(); }
  get loading() { return this.loadingSignal(); }
  get searchFilters() { return this.searchFiltersSignal(); }

  // Enums pour les templates
  SpaceType = SpaceType;
  SpaceStatus = SpaceStatus;
  Floor = Floor;

  // Options pour les filtres
  spaceTypeOptions: Array<{label: string, value: SpaceType | null}> = [];
  floorOptions: Array<{label: string, value: Floor | null}> = [];

  // Propriétés pour les filtres
  searchText = '';
  selectedType: SpaceType | null = null;
  selectedLocation = '';
  selectedFloor: Floor | null = null;
  minCapacity = 1;

  // FormControls pour la recherche et localisation avec debounce
  searchControl = new FormControl('');
  locationControl = new FormControl('');

  constructor(
    private spaceService: SpaceService,
    private router: Router,
    private message: NzMessageService,
    private modal: NzModalService,
    public enumMapper: EnumMapperService,
    private siteContext: SiteContextService
  ) {
    // Initialiser les options des filtres
    this.spaceTypeOptions = [
      { label: 'Tous les types', value: null },
      ...this.enumMapper.getSpaceTypeOptions()
    ];

    this.floorOptions = [
      { label: 'Tous les étages', value: null },
      ...this.enumMapper.getFloorOptions()
    ];
  }

  ngOnInit() {
    this.loadSpaces();

    // S'abonner aux changements de site pour recharger les données
    this.siteContext.currentSite$
      .pipe(takeUntil(this.destroy$))
      .subscribe(site => {
        if (site) {
          console.log('🔄 Site changed, reloading spaces data for:', site.name);
          this.loadSpaces();
        }
      });

    // S'abonner aux changements de recherche avec debounce
    this.searchControl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(searchTerm => {
        this.searchText = searchTerm || '';
        this.searchSpaces();
      });

    // S'abonner aux changements de localisation avec debounce
    this.locationControl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(location => {
        this.selectedLocation = location || '';
        this.searchSpaces();
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadSpaces() {
    this.loadingSignal.set(true);
    this.spaceService.getSpaces().subscribe({
      next: (spaces) => {
        this.spacesSignal.set(spaces);
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des espaces:', error);
        this.message.error('Erreur lors du chargement des espaces');
        this.loadingSignal.set(false);
      }
    });
  }

  searchSpaces() {
    const filters: SpaceSearchFilters = {
      search: this.searchText || undefined,
      type: this.selectedType || undefined,
      location: this.selectedLocation || undefined,
      floor: this.selectedFloor || undefined,
      capacity: this.minCapacity > 1 ? this.minCapacity : undefined
    };

    this.searchFiltersSignal.set(filters);
    this.loadingSignal.set(true);

    this.spaceService.searchSpaces(filters).subscribe({
      next: (spaces) => {
        this.spacesSignal.set(spaces);
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors de la recherche:', error);
        this.message.error('Erreur lors de la recherche');
        this.loadingSignal.set(false);
      }
    });
  }

  // Méthodes pour gérer les changements de filtres
  onTypeChange() {
    this.searchSpaces();
  }

  onFloorChange() {
    this.searchSpaces();
  }

  onCapacityChange() {
    this.searchSpaces();
  }

  clearFilters() {
    this.searchText = '';
    this.selectedType = null;
    this.selectedLocation = '';
    this.selectedFloor = null;
    this.minCapacity = 1;

    // Réinitialiser les FormControls
    this.searchControl.setValue('', { emitEvent: false });
    this.locationControl.setValue('', { emitEvent: false });

    this.searchFiltersSignal.set({});
    this.loadSpaces();
  }

  navigateToCreate() {
    this.router.navigate(['/spaces/new']);
  }

  navigateToDetails(space: Space) {
    this.router.navigate(['/spaces/details', space.id]);
  }

  navigateToEdit(space: Space) {
    this.router.navigate(['/spaces/edit', space.id]);
  }

  navigateToCalendar() {
    this.router.navigate(['/reservations']);
  }

  confirmDeleteSpace(space: Space) {
    this.modal.confirm({
      nzTitle: 'Supprimer l\'espace',
      nzContent: `Êtes-vous sûr de vouloir supprimer l'espace <strong>${space.name}</strong> ?<br><br>Cette action est irréversible et supprimera toutes les réservations associées.`,
      nzOkText: 'Supprimer',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzCancelText: 'Annuler',
      nzCentered: true,
      nzOnOk: () => {
        this.deleteSpace(space);
      }
    });
  }

  private deleteSpace(space: Space) {
    this.spaceService.deleteSpace(space.id).subscribe({
      next: () => {
        this.message.success('Espace supprimé avec succès');
        this.loadSpaces();
      },
      error: (error) => {
        console.error('Erreur lors de la suppression:', error);
        this.message.error('Erreur lors de la suppression');
      }
    });
  }

  // Méthodes utilitaires pour l'affichage
  getTypeLabel(type: SpaceType): string {
    switch (type) {
      case SpaceType.WORKSTATION:
        return 'Poste de travail';
      case SpaceType.PRIVATE_OFFICE:
        return 'Bureau privé';
      case SpaceType.MEETING_ROOM:
        return 'Salle de réunion';
      case SpaceType.PHONE_BOOTH:
        return 'Cabine téléphonique';
      case SpaceType.LOUNGE:
        return 'Espace détente';
      case SpaceType.CONFERENCE_ROOM:
        return 'Salle de conférence';
      case SpaceType.HOT_DESK:
        return 'Bureau partagé';
      case SpaceType.DEDICATED_DESK:
        return 'Bureau dédié';
      default:
        return type;
    }
  }

  getTypeColor(type: SpaceType): string {
    switch (type) {
      case SpaceType.WORKSTATION:
        return 'blue';
      case SpaceType.PRIVATE_OFFICE:
        return 'green';
      case SpaceType.MEETING_ROOM:
        return 'purple';
      case SpaceType.PHONE_BOOTH:
        return 'cyan';
      case SpaceType.LOUNGE:
        return 'orange';
      case SpaceType.CONFERENCE_ROOM:
        return 'red';
      case SpaceType.HOT_DESK:
        return 'geekblue';
      case SpaceType.DEDICATED_DESK:
        return 'gold';
      default:
        return 'default';
    }
  }

  getStatusColor(space: Space): string {
    switch (space.status) {
      case SpaceStatus.AVAILABLE:
        return 'success';
      case SpaceStatus.OCCUPIED:
        return 'warning';
      case SpaceStatus.SATURATED:
        return 'error';
      case SpaceStatus.MAINTENANCE:
        return 'processing';
      case SpaceStatus.OUT_OF_ORDER:
        return 'error';
      case SpaceStatus.RESERVED:
        return 'default';
      case SpaceStatus.UNAVAILABLE:
        return 'error';
      default:
        return 'default';
    }
  }

  getStatusText(space: Space): string {
    switch (space.status) {
      case SpaceStatus.AVAILABLE:
        return 'Disponible';
      case SpaceStatus.OCCUPIED:
        return 'Occupé';
      case SpaceStatus.SATURATED:
        return 'Saturé';
      case SpaceStatus.MAINTENANCE:
        return 'Maintenance';
      case SpaceStatus.OUT_OF_ORDER:
        return 'Hors service';
      case SpaceStatus.RESERVED:
        return 'Réservé';
      case SpaceStatus.UNAVAILABLE:
        return 'Indisponible';
      default:
        return space.status;
    }
  }

  getSpaceIcon(type: SpaceType): string {
    switch (type) {
      case SpaceType.WORKSTATION:
        return 'desktop';
      case SpaceType.PRIVATE_OFFICE:
        return 'home';
      case SpaceType.MEETING_ROOM:
        return 'team';
      case SpaceType.PHONE_BOOTH:
        return 'phone';
      case SpaceType.LOUNGE:
        return 'coffee';
      case SpaceType.CONFERENCE_ROOM:
        return 'video-camera';
      case SpaceType.HOT_DESK:
        return 'laptop';
      case SpaceType.DEDICATED_DESK:
        return 'bank';
      default:
        return 'environment';
    }
  }

  // Méthodes pour les statistiques (traitement côté TS)
  getTotalSpaces(): number {
    return this.spaces.length;
  }

  getAvailableSpacesCount(): number {
    return this.spaces.filter(s => s.status === SpaceStatus.AVAILABLE).length;
  }

  getOccupiedSpacesCount(): number {
    return this.spaces.filter(s => s.status === SpaceStatus.OCCUPIED).length;
  }

  getTotalCapacity(): number {
    return this.spaces
      .filter(s => s.status !== SpaceStatus.UNAVAILABLE)
      .reduce((sum, s) => sum + s.capacity, 0);
  }

  // Méthodes pour les styles des statistiques
  getAvailableSpacesStyle(): { [key: string]: string } {
    return { color: '#52c41a' };
  }

  getOccupiedSpacesStyle(): { [key: string]: string } {
    return { color: '#faad14' };
  }

  // Méthodes pour les équipements et commodités (traitement côté TS)
  getDisplayedEquipment(equipment: any[]): any[] {
    return equipment.slice(0, 3);
  }

  getRemainingEquipmentCount(equipment: any[]): number {
    return Math.max(0, equipment.length - 3);
  }

  hasMoreEquipment(equipment: any[]): boolean {
    return equipment.length > 3;
  }

  getDisplayedAmenities(amenities: string[]): string[] {
    return amenities.slice(0, 3);
  }

  getRemainingAmenitiesCount(amenities: string[]): number {
    return Math.max(0, amenities.length - 3);
  }

  hasMoreAmenities(amenities: string[]): boolean {
    return amenities.length > 3;
  }
}
