@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global Workeem Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #F2F2F7;
  color: #1C1C1E;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Override ng-zorro default styles for better Apple-like appearance */
.ant-layout {
  background: #F2F2F7 !important;
}

.ant-layout-sider {
  background: #FFFFFF !important;
}

.ant-menu-light {
  background: transparent !important;
  border: none !important;
}

.ant-menu-light .ant-menu-item {
  color: #1C1C1E !important;
  border-radius: 8px !important;
  margin: 4px 12px !important;
  width: calc(100% - 24px) !important;
  height: 48px !important;
  line-height: 48px !important;
  padding: 0 16px !important;
}

.ant-menu-light .ant-menu-item:hover {
  color: #6E56CF !important;
  background-color: #EDE9F8 !important;
}

.ant-menu-light .ant-menu-item-selected {
  background-color: #6E56CF !important;
  color: #FFFFFF !important;
}

.ant-menu-light .ant-menu-item-selected:hover {
  background-color: #5A47B8 !important;
  color: #FFFFFF !important;
}

.ant-menu-light .ant-menu-item-selected a {
  color: #FFFFFF !important;
}

/* Fix menu item overflow */
.ant-menu-light .ant-menu-item::after {
  display: none !important;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #F2F2F7;
}

::-webkit-scrollbar-thumb {
  background: #C7C7CC;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #AEAEB2;
}

/* Utility classes */
.text-primary {
  color: #6E56CF !important;
}

.bg-primary {
  background-color: #6E56CF !important;
}

.bg-primary-soft {
  background-color: #EDE9F8 !important;
}

.border-primary {
  border-color: #6E56CF !important;
}

.shadow-apple {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

.rounded-apple {
  border-radius: 8px !important;
}
