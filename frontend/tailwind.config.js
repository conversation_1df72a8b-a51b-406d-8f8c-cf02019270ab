/** @type {import('tailwindcss').Config} */
export default {
  content: ["./src/**/*.{html,ts,tsx}"],
  theme: {
    extend: {
      colors: {
        // Workeem Brand Colors
        primary: {
          DEFAULT: '#6E56CF', // Deep Indigo
          50: '#F5F3FF',
          100: '#EDE9F8', // Lavender Mist
          200: '#DDD4F3',
          300: '#CCBFEE',
          400: '#BBAAE9',
          500: '#6E56CF', // Main brand color
          600: '#5A47B8',
          700: '#4A3A9A',
          800: '#3A2D7C',
          900: '#2A205E',
        },
        // Apple-like neutrals
        gray: {
          50: '#F2F2F7', // Gris clair Apple-like
          100: '#E5E5EA',
          200: '#D1D1D6',
          300: '#C7C7CC',
          400: '#AEAEB2',
          500: '#8E8E93',
          600: '#636366',
          700: '#48484A',
          800: '#3A3A3C',
          900: '#1C1C1E', // Gris foncé Apple-like
        },
        // Semantic colors
        success: '#34C759',
        warning: '#FF9500',
        error: '#FF3B30',
        info: '#007AFF',
      },
      fontFamily: {
        sans: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
      },
      borderRadius: {
        'lg': '8px',
        'xl': '12px',
        '2xl': '16px',
      },
      boxShadow: {
        'apple': '0 2px 8px rgba(0, 0, 0, 0.06)',
        'apple-lg': '0 4px 16px rgba(0, 0, 0, 0.08)',
      },
    },
  },
  plugins: [],
}
