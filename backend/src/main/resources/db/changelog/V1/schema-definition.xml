<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <!-- Create Site table -->
    <changeSet id="001-create-site-table" author="abounass">
        <createTable tableName="sites">
            <column name="site_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="city" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="country" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="address" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <!-- Audit fields from BaseEntity -->
            <column name="created_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_selected_at" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Create indexes for better performance -->
        <createIndex tableName="sites" indexName="idx_site_name">
            <column name="name"/>
        </createIndex>

        <createIndex tableName="sites" indexName="idx_site_city">
            <column name="city"/>
        </createIndex>

        <createIndex tableName="sites" indexName="idx_site_country">
            <column name="country"/>
        </createIndex>

        <createIndex tableName="sites" indexName="idx_site_active">
            <column name="is_active"/>
        </createIndex>
    </changeSet>

    <!-- Insert sample data -->
    <changeSet id="002-insert-sample-sites" author="abounass">
        <insert tableName="sites">
            <column name="name" value="Workeem Paris"/>
            <column name="city" value="Paris"/>
            <column name="country" value="France"/>
            <column name="address" value="123 Avenue des Champs-Élysées, 75008 Paris"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="sites">
            <column name="name" value="Workeem Lyon"/>
            <column name="city" value="Lyon"/>
            <column name="country" value="France"/>
            <column name="address" value="45 Rue de la République, 69002 Lyon"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="sites">
            <column name="name" value="Workeem Marseille"/>
            <column name="city" value="Marseille"/>
            <column name="country" value="France"/>
            <column name="address" value="78 La Canebière, 13001 Marseille"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="sites">
            <column name="name" value="Workeem Bordeaux"/>
            <column name="city" value="Bordeaux"/>
            <column name="country" value="France"/>
            <column name="address" value="12 Cours de l'Intendance, 33000 Bordeaux"/>
            <column name="is_active" valueBoolean="false"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>
    </changeSet>

    <!-- Create spaces table -->
    <changeSet id="3" author="abounass">
        <createTable tableName="spaces">
            <column name="space_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="site_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="type" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="capacity" type="INT">
                <constraints nullable="true"/>
            </column>
            <column name="location" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="area" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <column name="images" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="rules" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="hourly_rate" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <column name="daily_rate" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <column name="weekly_rate" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <column name="monthly_rate" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <column name="floor" type="VARCHAR(50)" defaultValue="GROUND_FLOOR">
                <constraints nullable="false"/>
            </column>
            <column name="amenities" type="VARCHAR(1000)">
                <constraints nullable="true"/>
            </column>
            <column name="created_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Add foreign key constraint to sites -->
        <addForeignKeyConstraint
                baseTableName="spaces"
                baseColumnNames="site_id"
                constraintName="fk_spaces_site_id"
                referencedTableName="sites"
                referencedColumnNames="site_id"
                onDelete="CASCADE"/>

        <!-- Create indexes for spaces -->
        <createIndex tableName="spaces" indexName="idx_space_site_id">
            <column name="site_id"/>
        </createIndex>

        <createIndex tableName="spaces" indexName="idx_space_name">
            <column name="name"/>
        </createIndex>

        <createIndex tableName="spaces" indexName="idx_space_type">
            <column name="type"/>
        </createIndex>

        <createIndex tableName="spaces" indexName="idx_space_active">
            <column name="is_active"/>
        </createIndex>
    </changeSet>



    <!-- Create equipment table -->
    <changeSet id="5" author="abounass">
        <createTable tableName="equipment">
            <column name="equipment_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="space_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="brand" type="VARCHAR(100)">
                <constraints nullable="true"/>
            </column>
            <column name="model" type="VARCHAR(100)">
                <constraints nullable="true"/>
            </column>
            <column name="quantity" type="INT" defaultValueNumeric="1">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(50)" defaultValue="WORKING">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="created_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Foreign key constraint -->
        <addForeignKeyConstraint
            baseTableName="equipment"
            baseColumnNames="space_id"
            constraintName="fk_equipment_space"
            referencedTableName="spaces"
            referencedColumnNames="space_id"
            onDelete="CASCADE"/>

        <!-- Create indexes -->
        <createIndex tableName="equipment" indexName="idx_equipment_space_id">
            <column name="space_id"/>
        </createIndex>
        <createIndex tableName="equipment" indexName="idx_equipment_type">
            <column name="type"/>
        </createIndex>
        <createIndex tableName="equipment" indexName="idx_equipment_status">
            <column name="status"/>
        </createIndex>
    </changeSet>

    <!-- Create space_availability table -->
    <changeSet id="6" author="abounass">
        <createTable tableName="space_availability">
            <column name="availability_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="space_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <column name="advance_booking_days" type="INT" defaultValueNumeric="30">
                <constraints nullable="false"/>
            </column>
            <column name="min_booking_duration" type="INT" defaultValueNumeric="60">
                <constraints nullable="false"/>
            </column>
            <column name="max_booking_duration" type="INT" defaultValueNumeric="480">
                <constraints nullable="false"/>
            </column>
            <column name="buffer_time" type="INT" defaultValueNumeric="15">
                <constraints nullable="false"/>
            </column>
            <column name="weekly_schedule" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="exceptions" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="created_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Foreign key constraint -->
        <addForeignKeyConstraint
            baseTableName="space_availability"
            baseColumnNames="space_id"
            constraintName="fk_availability_space"
            referencedTableName="spaces"
            referencedColumnNames="space_id"
            onDelete="CASCADE"/>

        <!-- Create index -->
        <createIndex tableName="space_availability" indexName="idx_availability_space_id">
            <column name="space_id"/>
        </createIndex>
    </changeSet>

    <!-- Create space_pricing table -->
    <changeSet id="7" author="abounass">
        <createTable tableName="space_pricing">
            <column name="pricing_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="space_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="hourly_rate" type="DECIMAL(10,2)" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="daily_rate" type="DECIMAL(10,2)" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="weekly_rate" type="DECIMAL(10,2)" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="monthly_rate" type="DECIMAL(10,2)" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="currency" type="VARCHAR(10)" defaultValue="EUR">
                <constraints nullable="false"/>
            </column>
            <column name="discounts" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="created_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Foreign key constraint -->
        <addForeignKeyConstraint
            baseTableName="space_pricing"
            baseColumnNames="space_id"
            constraintName="fk_pricing_space"
            referencedTableName="spaces"
            referencedColumnNames="space_id"
            onDelete="CASCADE"/>

        <!-- Create index -->
        <createIndex tableName="space_pricing" indexName="idx_pricing_space_id">
            <column name="space_id"/>
        </createIndex>
    </changeSet>

    <!-- Insert sample spaces data -->
    <changeSet id="8" author="abounass">
        <!-- Poste de travail A1 -->
        <insert tableName="spaces">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Poste de travail A1"/>
            <column name="description" value="Poste de travail individuel avec vue sur jardin"/>
            <column name="type" value="WORKSTATION"/>
            <column name="capacity" valueNumeric="1"/>
            <column name="location" value="Zone A"/>
            <column name="floor" value="GROUND_FLOOR"/>
            <column name="area" valueNumeric="4.0"/>
            <column name="amenities" value="WiFi,Prise électrique,Éclairage LED"/>
            <column name="rules" value="Maintenir l'espace propre,Pas de nourriture,Silence requis"/>
            <column name="hourly_rate" valueNumeric="8.0"/>
            <column name="daily_rate" valueNumeric="50.0"/>
            <column name="weekly_rate" valueNumeric="300.0"/>
            <column name="monthly_rate" valueNumeric="1000.0"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <!-- Bureau privé B1 -->
        <insert tableName="spaces">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Bureau privé B1"/>
            <column name="description" value="Bureau privé pour 2 personnes avec équipement complet"/>
            <column name="type" value="PRIVATE_OFFICE"/>
            <column name="capacity" valueNumeric="2"/>
            <column name="location" value="Zone B"/>
            <column name="floor" value="FIRST_FLOOR"/>
            <column name="area" valueNumeric="12.0"/>
            <column name="amenities" value="WiFi,Climatisation,Fenêtre,Tableau blanc"/>
            <column name="rules" value="Accès par badge,Nettoyage quotidien inclus,Pas de visiteurs sans autorisation"/>
            <column name="hourly_rate" valueNumeric="25.0"/>
            <column name="daily_rate" valueNumeric="180.0"/>
            <column name="weekly_rate" valueNumeric="1000.0"/>
            <column name="monthly_rate" valueNumeric="3500.0"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <!-- Salle de réunion Alpha -->
        <insert tableName="spaces">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Salle de réunion Alpha"/>
            <column name="description" value="Salle de réunion moderne pour 8 personnes avec équipement audiovisuel"/>
            <column name="type" value="MEETING_ROOM"/>
            <column name="capacity" valueNumeric="8"/>
            <column name="location" value="Zone C"/>
            <column name="floor" value="SECOND_FLOOR"/>
            <column name="area" valueNumeric="25.0"/>
            <column name="amenities" value="WiFi haut débit,Climatisation,Éclairage modulable,Isolation phonique"/>
            <column name="rules" value="Réservation obligatoire,Nettoyage après usage,Matériel à remettre en place"/>
            <column name="hourly_rate" valueNumeric="45.0"/>
            <column name="daily_rate" valueNumeric="320.0"/>
            <column name="weekly_rate" valueNumeric="1800.0"/>
            <column name="monthly_rate" valueNumeric="6000.0"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <!-- Espace Collaboratif Principal -->
        <insert tableName="spaces">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Espace Collaboratif Principal"/>
            <column name="description" value="Grand espace ouvert pour le travail collaboratif, accessible sans réservation"/>
            <column name="type" value="COLLABORATIVE"/>
            <column name="capacity" valueNumeric="40"/>
            <column name="location" value="Rez-de-chaussée"/>
            <column name="floor" value="GROUND_FLOOR"/>
            <column name="area" valueNumeric="120.0"/>
            <column name="amenities" value="WiFi haut débit,Prises électriques multiples,Éclairage naturel,Mobilier ergonomique,Espaces détente,Machine à café,Imprimante partagée"/>
            <column name="rules" value="Accès libre pendant les heures d'ouverture,Respect du silence relatif,Nettoyage après usage"/>
            <column name="hourly_rate" valueNumeric="0.0"/>
            <column name="daily_rate" valueNumeric="0.0"/>
            <column name="weekly_rate" valueNumeric="0.0"/>
            <column name="monthly_rate" valueNumeric="0.0"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <!-- Cabine téléphonique 1 -->
        <insert tableName="spaces">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Cabine téléphonique 1"/>
            <column name="description" value="Cabine isolée pour appels confidentiels"/>
            <column name="type" value="PHONE_BOOTH"/>
            <column name="capacity" valueNumeric="1"/>
            <column name="location" value="Zone A"/>
            <column name="floor" value="GROUND_FLOOR"/>
            <column name="area" valueNumeric="2.0"/>
            <column name="amenities" value="Isolation phonique,Éclairage,Ventilation"/>
            <column name="rules" value="Durée maximale 30 minutes,Pas de nourriture"/>
            <column name="hourly_rate" valueNumeric="5.0"/>
            <column name="daily_rate" valueNumeric="30.0"/>
            <column name="weekly_rate" valueNumeric="150.0"/>
            <column name="monthly_rate" valueNumeric="500.0"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>
    </changeSet>

    <!-- Create Members table -->
    <changeSet id="008-create-members-table" author="abounass">
        <createTable tableName="members">
            <column name="member_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="site_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="first_name" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="last_name" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="email" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="phone" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
            <column name="company" type="VARCHAR(100)">
                <constraints nullable="true"/>
            </column>
            <column name="member_type" type="VARCHAR(20)" defaultValue="PROFESSIONAL">
                <constraints nullable="false"/>
            </column>
            <column name="student_code" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
            <column name="ice_number" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
            <column name="subscription_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="status" type="VARCHAR(20)" defaultValue="ACTIVE">
                <constraints nullable="false"/>
            </column>
            <!-- Audit fields from BaseEntity -->
            <column name="created_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Add foreign key constraints -->
        <addForeignKeyConstraint
                baseTableName="members"
                baseColumnNames="site_id"
                constraintName="fk_members_site_id"
                referencedTableName="sites"
                referencedColumnNames="site_id"
                onDelete="CASCADE"/>

        <addForeignKeyConstraint
                baseTableName="members"
                baseColumnNames="subscription_id"
                constraintName="fk_members_subscription_id"
                referencedTableName="subscriptions"
                referencedColumnNames="subscription_id"
                onDelete="SET NULL"/>

        <!-- Create indexes -->
        <createIndex tableName="members" indexName="idx_member_site_id">
            <column name="site_id"/>
        </createIndex>

        <createIndex tableName="members" indexName="idx_member_email">
            <column name="email"/>
        </createIndex>

        <createIndex tableName="members" indexName="idx_member_type">
            <column name="member_type"/>
        </createIndex>

        <createIndex tableName="members" indexName="idx_student_code">
            <column name="student_code"/>
        </createIndex>

        <createIndex tableName="members" indexName="idx_ice_number">
            <column name="ice_number"/>
        </createIndex>

        <createIndex tableName="members" indexName="idx_subscription_id">
            <column name="subscription_id"/>
        </createIndex>
    </changeSet>

    <!-- Create Reservations table -->
    <changeSet id="009-create-reservations-table" author="abounass">
        <createTable tableName="reservations">
            <column name="reservation_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="site_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="space_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="member_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="start_time" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="end_time" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="purpose" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="status" type="VARCHAR(20)" defaultValue="PENDING">
                <constraints nullable="false"/>
            </column>
            <column name="number_of_people" type="INTEGER">
                <constraints nullable="true"/>
            </column>
            <column name="notes" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="recurrence" type="VARCHAR(20)" defaultValue="NONE">
                <constraints nullable="true"/>
            </column>
            <column name="total_cost" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <!-- Audit fields from BaseEntity -->
            <column name="created_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Add foreign key constraints -->
        <addForeignKeyConstraint
                baseTableName="reservations"
                baseColumnNames="site_id"
                constraintName="fk_reservations_site_id"
                referencedTableName="sites"
                referencedColumnNames="site_id"
                onDelete="CASCADE"/>

        <addForeignKeyConstraint
                baseTableName="reservations"
                baseColumnNames="space_id"
                constraintName="fk_reservation_space"
                referencedTableName="spaces"
                referencedColumnNames="space_id"
                onDelete="CASCADE"/>

        <addForeignKeyConstraint
                baseTableName="reservations"
                baseColumnNames="member_id"
                constraintName="fk_reservation_member"
                referencedTableName="members"
                referencedColumnNames="member_id"
                onDelete="CASCADE"/>

        <!-- Create indexes for better performance -->
        <createIndex tableName="reservations" indexName="idx_reservation_site_id">
            <column name="site_id"/>
        </createIndex>

        <createIndex tableName="reservations" indexName="idx_reservation_space_id">
            <column name="space_id"/>
        </createIndex>

        <createIndex tableName="reservations" indexName="idx_reservation_member_id">
            <column name="member_id"/>
        </createIndex>

        <createIndex tableName="reservations" indexName="idx_reservation_status">
            <column name="status"/>
        </createIndex>

        <createIndex tableName="reservations" indexName="idx_reservation_start_time">
            <column name="start_time"/>
        </createIndex>

        <createIndex tableName="reservations" indexName="idx_reservation_date_range">
            <column name="start_time"/>
            <column name="end_time"/>
        </createIndex>
    </changeSet>



    <!-- Insert test members data -->
    <changeSet id="011-insert-test-members" author="abounass">
        <insert tableName="members">
            <column name="site_id" valueNumeric="1"/>
            <column name="first_name" value="Aicha"/>
            <column name="last_name" value="Benali"/>
            <column name="email" value="<EMAIL>"/>
            <column name="phone" value="+212 6 12 34 56 78"/>
            <column name="company" value=""/>
            <column name="member_type" value="STUDENT"/>
            <column name="student_code" value="ETU2024001"/>
            <column name="status" value="ACTIVE"/>
            <column name="created_by" value="system"/>
            <column name="created_date" valueDate="2024-01-15"/>
            <column name="last_modified_by" value="system"/>
            <column name="last_modified_date" valueDate="2024-01-15"/>
        </insert>

        <insert tableName="members">
            <column name="site_id" valueNumeric="1"/>
            <column name="first_name" value="Omar"/>
            <column name="last_name" value="El Fassi"/>
            <column name="email" value="<EMAIL>"/>
            <column name="phone" value="+212 6 98 76 54 32"/>
            <column name="company" value="TechCorp"/>
            <column name="member_type" value="COMPANY"/>
            <column name="ice_number" value="ICE000000002"/>
            <column name="status" value="ACTIVE"/>
            <column name="created_by" value="system"/>
            <column name="created_date" valueDate="2024-02-01"/>
            <column name="last_modified_by" value="system"/>
            <column name="last_modified_date" valueDate="2024-02-01"/>
        </insert>

        <insert tableName="members">
            <column name="site_id" valueNumeric="1"/>
            <column name="first_name" value="Fatima"/>
            <column name="last_name" value="Zahra"/>
            <column name="email" value="<EMAIL>"/>
            <column name="phone" value="+212 6 11 22 33 44"/>
            <column name="company" value=""/>
            <column name="member_type" value="STUDENT"/>
            <column name="student_code" value="ETU2024003"/>
            <column name="status" value="INACTIVE"/>
            <column name="created_by" value="system"/>
            <column name="created_date" valueDate="2024-01-20"/>
            <column name="last_modified_by" value="system"/>
            <column name="last_modified_date" valueDate="2024-01-25"/>
        </insert>

        <insert tableName="members">
            <column name="site_id" valueNumeric="1"/>
            <column name="first_name" value="Houssam"/>
            <column name="last_name" value="Alaoui"/>
            <column name="email" value="<EMAIL>"/>
            <column name="phone" value="+212 6 55 44 33 22"/>
            <column name="company" value="InnovateMA"/>
            <column name="member_type" value="COMPANY"/>
            <column name="ice_number" value="ICE000000004"/>
            <column name="status" value="ACTIVE"/>
            <column name="created_by" value="system"/>
            <column name="created_date" valueDate="2024-02-10"/>
            <column name="last_modified_by" value="system"/>
            <column name="last_modified_date" valueDate="2024-02-10"/>
        </insert>

        <insert tableName="members">
            <column name="site_id" valueNumeric="1"/>
            <column name="first_name" value="Khadija"/>
            <column name="last_name" value="Benjelloun"/>
            <column name="email" value="<EMAIL>"/>
            <column name="phone" value="+212 6 77 88 99 00"/>
            <column name="company" value=""/>
            <column name="member_type" value="STUDENT"/>
            <column name="student_code" value="ETU2024005"/>
            <column name="status" value="ACTIVE"/>
            <column name="created_by" value="system"/>
            <column name="created_date" valueDate="2024-02-15"/>
            <column name="last_modified_by" value="system"/>
            <column name="last_modified_date" valueDate="2024-02-15"/>
        </insert>

        <insert tableName="members">
            <column name="site_id" valueNumeric="1"/>
            <column name="first_name" value="Hassan"/>
            <column name="last_name" value="Tazi"/>
            <column name="email" value="<EMAIL>"/>
            <column name="phone" value="+212 6 33 22 11 00"/>
            <column name="company" value="FreelanceMA"/>
            <column name="member_type" value="PROFESSIONAL"/>
            <column name="status" value="INACTIVE"/>
            <column name="created_by" value="system"/>
            <column name="created_date" valueDate="2024-01-30"/>
            <column name="last_modified_by" value="system"/>
            <column name="last_modified_date" valueDate="2024-02-20"/>
        </insert>

        <!-- Members pour le site 2 pour tester le changement de site -->
        <insert tableName="members">
            <column name="site_id" valueNumeric="2"/>
            <column name="first_name" value="Youssef"/>
            <column name="last_name" value="Amrani"/>
            <column name="email" value="<EMAIL>"/>
            <column name="phone" value="+212 6 44 55 66 77"/>
            <column name="company" value=""/>
            <column name="member_type" value="STUDENT"/>
            <column name="student_code" value="ETU2024006"/>
            <column name="status" value="ACTIVE"/>
            <column name="created_by" value="system"/>
            <column name="created_date" valueDate="2024-03-01"/>
            <column name="last_modified_by" value="system"/>
            <column name="last_modified_date" valueDate="2024-03-01"/>
        </insert>

        <insert tableName="members">
            <column name="site_id" valueNumeric="2"/>
            <column name="first_name" value="Salma"/>
            <column name="last_name" value="Berrada"/>
            <column name="email" value="<EMAIL>"/>
            <column name="phone" value="+212 6 88 99 00 11"/>
            <column name="company" value="BusinessCorp"/>
            <column name="member_type" value="COMPANY"/>
            <column name="ice_number" value="ICE000000007"/>
            <column name="status" value="ACTIVE"/>
            <column name="created_by" value="system"/>
            <column name="created_date" valueDate="2024-03-05"/>
            <column name="last_modified_by" value="system"/>
            <column name="last_modified_date" valueDate="2024-03-05"/>
        </insert>
    </changeSet>



    <!-- Create Subscriptions table -->
    <changeSet id="013-create-subscriptions-table" author="abounass">
        <createTable tableName="subscriptions">
            <column name="subscription_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="site_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="price" type="DECIMAL(10,2)">
                <constraints nullable="false"/>
            </column>
            <column name="currency" type="VARCHAR(10)" defaultValue="MAD">
                <constraints nullable="false"/>
            </column>
            <column name="duration_days" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="member_type" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
            <column name="features" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="max_reservations_per_day" type="INTEGER" defaultValueNumeric="3">
                <constraints nullable="false"/>
            </column>
            <column name="max_reservations_per_week" type="INTEGER" defaultValueNumeric="15">
                <constraints nullable="false"/>
            </column>
            <column name="max_reservations_per_month" type="INTEGER" defaultValueNumeric="60">
                <constraints nullable="false"/>
            </column>
            <column name="max_consecutive_hours" type="INTEGER" defaultValueNumeric="8">
                <constraints nullable="false"/>
            </column>
            <column name="advance_booking_days" type="INTEGER" defaultValueNumeric="30">
                <constraints nullable="false"/>
            </column>
            <column name="can_book_meeting_rooms" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <column name="can_access_premium_areas" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <!-- Audit fields from BaseEntity -->
            <column name="created_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Create indexes -->
        <createIndex tableName="subscriptions" indexName="idx_subscription_site_id">
            <column name="site_id"/>
        </createIndex>

        <createIndex tableName="subscriptions" indexName="idx_subscription_member_type">
            <column name="member_type"/>
        </createIndex>

        <createIndex tableName="subscriptions" indexName="idx_subscription_active">
            <column name="is_active"/>
        </createIndex>

        <!-- Add foreign key constraint -->
        <addForeignKeyConstraint
                baseTableName="subscriptions"
                baseColumnNames="site_id"
                constraintName="fk_subscriptions_site_id"
                referencedTableName="sites"
                referencedColumnNames="site_id"
                onDelete="CASCADE"/>
    </changeSet>

    <!-- Add subscription_id column to members table -->
    <changeSet id="014-add-subscription-id-to-members" author="abounass">
        <addColumn tableName="members">
            <column name="subscription_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <!-- Create index for better performance -->
        <createIndex tableName="members" indexName="idx_subscription_id">
            <column name="subscription_id"/>
        </createIndex>

        <!-- Add foreign key constraint -->
        <addForeignKeyConstraint
                baseTableName="members"
                baseColumnNames="subscription_id"
                constraintName="fk_members_subscription_id"
                referencedTableName="subscriptions"
                referencedColumnNames="subscription_id"
                onDelete="SET NULL"/>
    </changeSet>



    <!-- Insert default subscriptions data -->
    <changeSet id="015-insert-default-subscriptions" author="abounass">
        <!-- Abonnements pour étudiants -->
        <insert tableName="subscriptions">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Étudiant Basique"/>
            <column name="description" value="Abonnement basique pour étudiants avec accès limité"/>
            <column name="price" valueNumeric="50.00"/>
            <column name="currency" value="MAD"/>
            <column name="duration_days" valueNumeric="30"/>
            <column name="member_type" value="STUDENT"/>
            <column name="features" value="Accès espaces de coworking,WiFi gratuit,2h par jour maximum"/>
            <column name="max_reservations_per_day" valueNumeric="1"/>
            <column name="max_reservations_per_week" valueNumeric="5"/>
            <column name="max_reservations_per_month" valueNumeric="20"/>
            <column name="max_consecutive_hours" valueNumeric="2"/>
            <column name="advance_booking_days" valueNumeric="7"/>
            <column name="can_book_meeting_rooms" valueBoolean="false"/>
            <column name="can_access_premium_areas" valueBoolean="false"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="subscriptions">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Étudiant Premium"/>
            <column name="description" value="Abonnement premium pour étudiants avec plus d'avantages"/>
            <column name="price" valueNumeric="80.00"/>
            <column name="currency" value="MAD"/>
            <column name="duration_days" valueNumeric="30"/>
            <column name="member_type" value="STUDENT"/>
            <column name="features" value="Accès espaces de coworking,WiFi gratuit,4h par jour maximum,Accès salles de réunion"/>
            <column name="max_reservations_per_day" valueNumeric="2"/>
            <column name="max_reservations_per_week" valueNumeric="10"/>
            <column name="max_reservations_per_month" valueNumeric="40"/>
            <column name="max_consecutive_hours" valueNumeric="4"/>
            <column name="advance_booking_days" valueNumeric="14"/>
            <column name="can_book_meeting_rooms" valueBoolean="true"/>
            <column name="can_access_premium_areas" valueBoolean="false"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <!-- Abonnements pour professionnels -->
        <insert tableName="subscriptions">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Professionnel Standard"/>
            <column name="description" value="Abonnement standard pour professionnels"/>
            <column name="price" valueNumeric="120.00"/>
            <column name="currency" value="MAD"/>
            <column name="duration_days" valueNumeric="30"/>
            <column name="member_type" value="PROFESSIONAL"/>
            <column name="features" value="Accès illimité espaces de coworking,WiFi gratuit,Salles de réunion,Support technique"/>
            <column name="max_reservations_per_day" valueNumeric="3"/>
            <column name="max_reservations_per_week" valueNumeric="15"/>
            <column name="max_reservations_per_month" valueNumeric="60"/>
            <column name="max_consecutive_hours" valueNumeric="8"/>
            <column name="advance_booking_days" valueNumeric="30"/>
            <column name="can_book_meeting_rooms" valueBoolean="true"/>
            <column name="can_access_premium_areas" valueBoolean="true"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="subscriptions">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Professionnel Premium"/>
            <column name="description" value="Abonnement premium pour professionnels avec tous les avantages"/>
            <column name="price" valueNumeric="200.00"/>
            <column name="currency" value="MAD"/>
            <column name="duration_days" valueNumeric="30"/>
            <column name="member_type" value="PROFESSIONAL"/>
            <column name="features" value="Accès illimité,WiFi gratuit,Salles de réunion prioritaires,Support technique premium,Espaces privés"/>
            <column name="max_reservations_per_day" valueNumeric="5"/>
            <column name="max_reservations_per_week" valueNumeric="25"/>
            <column name="max_reservations_per_month" valueNumeric="100"/>
            <column name="max_consecutive_hours" valueNumeric="12"/>
            <column name="advance_booking_days" valueNumeric="60"/>
            <column name="can_book_meeting_rooms" valueBoolean="true"/>
            <column name="can_access_premium_areas" valueBoolean="true"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <!-- Abonnements pour entreprises -->
        <insert tableName="subscriptions">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Entreprise Domiciliation"/>
            <column name="description" value="Abonnement pour domiciliation d'entreprise"/>
            <column name="price" valueNumeric="300.00"/>
            <column name="currency" value="MAD"/>
            <column name="duration_days" valueNumeric="30"/>
            <column name="member_type" value="COMPANY"/>
            <column name="features" value="Adresse commerciale,Réception courrier,Salles de réunion,Espaces de coworking,Support administratif"/>
            <column name="max_reservations_per_day" valueNumeric="10"/>
            <column name="max_reservations_per_week" valueNumeric="50"/>
            <column name="max_reservations_per_month" valueNumeric="200"/>
            <column name="max_consecutive_hours" valueNumeric="24"/>
            <column name="advance_booking_days" valueNumeric="90"/>
            <column name="can_book_meeting_rooms" valueBoolean="true"/>
            <column name="can_access_premium_areas" valueBoolean="true"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

    </changeSet>

    <!-- Insert additional subscriptions (half-yearly and yearly) -->
    <changeSet id="016-insert-additional-subscriptions" author="abounass">
        <!-- Abonnements semestriels -->
        <insert tableName="subscriptions">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Étudiant Semestriel"/>
            <column name="description" value="Abonnement semestriel pour étudiants avec tarif avantageux"/>
            <column name="price" valueNumeric="400.00"/>
            <column name="currency" value="MAD"/>
            <column name="duration_days" valueNumeric="183"/>
            <column name="member_type" value="STUDENT"/>
            <column name="features" value="Accès espaces de coworking,WiFi gratuit,6h par jour maximum,Accès salles de réunion,Tarif étudiant"/>
            <column name="max_reservations_per_day" valueNumeric="3"/>
            <column name="max_reservations_per_week" valueNumeric="18"/>
            <column name="max_reservations_per_month" valueNumeric="75"/>
            <column name="max_consecutive_hours" valueNumeric="6"/>
            <column name="advance_booking_days" valueNumeric="21"/>
            <column name="can_book_meeting_rooms" valueBoolean="true"/>
            <column name="can_access_premium_areas" valueBoolean="false"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="subscriptions">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Professionnel Semestriel"/>
            <column name="description" value="Abonnement semestriel pour professionnels avec avantages étendus"/>
            <column name="price" valueNumeric="650.00"/>
            <column name="currency" value="MAD"/>
            <column name="duration_days" valueNumeric="183"/>
            <column name="member_type" value="PROFESSIONAL"/>
            <column name="features" value="Accès illimité espaces de coworking,WiFi gratuit,Salles de réunion prioritaires,Support technique,Espaces premium"/>
            <column name="max_reservations_per_day" valueNumeric="6"/>
            <column name="max_reservations_per_week" valueNumeric="35"/>
            <column name="max_reservations_per_month" valueNumeric="150"/>
            <column name="max_consecutive_hours" valueNumeric="10"/>
            <column name="advance_booking_days" valueNumeric="45"/>
            <column name="can_book_meeting_rooms" valueBoolean="true"/>
            <column name="can_access_premium_areas" valueBoolean="true"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <!-- Abonnements annuels -->
        <insert tableName="subscriptions">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Étudiant Annuel"/>
            <column name="description" value="Abonnement annuel pour étudiants - Meilleur rapport qualité/prix"/>
            <column name="price" valueNumeric="720.00"/>
            <column name="currency" value="MAD"/>
            <column name="duration_days" valueNumeric="365"/>
            <column name="member_type" value="STUDENT"/>
            <column name="features" value="Accès espaces de coworking,WiFi gratuit,8h par jour maximum,Accès salles de réunion,Casier personnel,Support prioritaire"/>
            <column name="max_reservations_per_day" valueNumeric="4"/>
            <column name="max_reservations_per_week" valueNumeric="25"/>
            <column name="max_reservations_per_month" valueNumeric="100"/>
            <column name="max_consecutive_hours" valueNumeric="8"/>
            <column name="advance_booking_days" valueNumeric="30"/>
            <column name="can_book_meeting_rooms" valueBoolean="true"/>
            <column name="can_access_premium_areas" valueBoolean="true"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="subscriptions">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Professionnel Annuel"/>
            <column name="description" value="Abonnement annuel pour professionnels - Formule complète"/>
            <column name="price" valueNumeric="1200.00"/>
            <column name="currency" value="MAD"/>
            <column name="duration_days" valueNumeric="365"/>
            <column name="member_type" value="PROFESSIONAL"/>
            <column name="features" value="Accès illimité,WiFi gratuit,Salles de réunion prioritaires,Support technique premium,Espaces premium,Conciergerie,Parking"/>
            <column name="max_reservations_per_day" valueNumeric="8"/>
            <column name="max_reservations_per_week" valueNumeric="50"/>
            <column name="max_reservations_per_month" valueNumeric="200"/>
            <column name="max_consecutive_hours" valueNumeric="16"/>
            <column name="advance_booking_days" valueNumeric="90"/>
            <column name="can_book_meeting_rooms" valueBoolean="true"/>
            <column name="can_access_premium_areas" valueBoolean="true"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="subscriptions">
            <column name="site_id" valueNumeric="1"/>
            <column name="name" value="Entreprise Annuel"/>
            <column name="description" value="Abonnement annuel pour entreprises - Solution complète"/>
            <column name="price" valueNumeric="2400.00"/>
            <column name="currency" value="MAD"/>
            <column name="duration_days" valueNumeric="365"/>
            <column name="member_type" value="COMPANY"/>
            <column name="features" value="Adresse commerciale,Réception courrier,Salles de réunion illimitées,Espaces de coworking,Support administratif,Conciergerie premium,Parking réservé"/>
            <column name="max_reservations_per_day" valueNumeric="20"/>
            <column name="max_reservations_per_week" valueNumeric="100"/>
            <column name="max_reservations_per_month" valueNumeric="400"/>
            <column name="max_consecutive_hours" valueNumeric="24"/>
            <column name="advance_booking_days" valueNumeric="180"/>
            <column name="can_book_meeting_rooms" valueBoolean="true"/>
            <column name="can_access_premium_areas" valueBoolean="true"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>
    </changeSet>



</databaseChangeLog>