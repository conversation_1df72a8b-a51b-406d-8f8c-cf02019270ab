package com.workeem.workeem_api.business.subscription.infrastructure;

import com.workeem.workeem_api.business.member.domain.MemberType;
import com.workeem.workeem_api.business.subscription.domain.Subscription;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SubscriptionRepository extends JpaRepository<Subscription, Long> {

    /**
     * Find all subscriptions for a specific site
     */
    List<Subscription> findBySiteId(Long siteId);

    /**
     * Find active subscriptions for a specific site
     */
    List<Subscription> findBySiteIdAndIsActive(Long siteId, Boolean isActive);

    /**
     * Find subscriptions by member type for a specific site
     */
    List<Subscription> findBySiteIdAndMemberType(Long siteId, MemberType memberType);

    /**
     * Find active subscriptions by member type for a specific site
     */
    List<Subscription> findBySiteIdAndMemberTypeAndIsActive(Long siteId, MemberType memberType, Boolean isActive);

    /**
     * Find subscriptions compatible with any member type (memberType is null) for a specific site
     */
    List<Subscription> findBySiteIdAndMemberTypeIsNull(Long siteId);

    /**
     * Find active subscriptions compatible with any member type for a specific site
     */
    List<Subscription> findBySiteIdAndMemberTypeIsNullAndIsActive(Long siteId, Boolean isActive);

    /**
     * Count subscriptions by site
     */
    long countBySiteId(Long siteId);

    /**
     * Count active subscriptions by site
     */
    long countBySiteIdAndIsActive(Long siteId, Boolean isActive);

    /**
     * Find all subscriptions for a specific site with pagination
     */
    Page<Subscription> findBySiteId(Long siteId, Pageable pageable);

    /**
     * Find active subscriptions for a specific site with pagination
     */
    Page<Subscription> findBySiteIdAndIsActive(Long siteId, Boolean isActive, Pageable pageable);

    /**
     * Find subscriptions by member type for a specific site with pagination
     */
    Page<Subscription> findBySiteIdAndMemberType(Long siteId, MemberType memberType, Pageable pageable);

    /**
     * Find active subscriptions by member type for a specific site with pagination
     */
    Page<Subscription> findBySiteIdAndMemberTypeAndIsActive(Long siteId, MemberType memberType, Boolean isActive, Pageable pageable);

    /**
     * Search subscriptions by name or description with optional filters
     */
    @Query("SELECT s FROM Subscription s WHERE s.siteId = :siteId " +
           "AND (LOWER(s.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(s.description) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) " +
           "AND (:active IS NULL OR s.isActive = :active) " +
           "AND (:memberType IS NULL OR s.memberType = :memberType)")
    Page<Subscription> searchSubscriptions(@Param("siteId") Long siteId,
                                         @Param("searchTerm") String searchTerm,
                                         @Param("active") Boolean active,
                                         @Param("memberType") MemberType memberType,
                                         Pageable pageable);
}
