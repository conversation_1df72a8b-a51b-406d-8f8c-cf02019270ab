package com.workeem.workeem_api.business.member.web;

import com.workeem.workeem_api.business.member.application.MemberService;
import com.workeem.workeem_api.business.member.domain.Member;
import com.workeem.workeem_api.business.member.domain.MemberType;
import com.workeem.workeem_api.business.member.domain.MemberStatus;
import com.workeem.workeem_api.business.member.web.dto.MemberRequestDto;
import com.workeem.workeem_api.business.member.web.dto.MemberResponseDto;
import com.workeem.workeem_api.business.subscription.application.SubscriptionService;
import com.workeem.workeem_api.business.subscription.domain.Subscription;
import com.workeem.workeem_api.shared.dto.PageRequestDto;
import com.workeem.workeem_api.shared.dto.PageResponseDto;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.security.access.prepost.PreAuthorize;
import static com.workeem.workeem_api.shared.security.PlanAuthorizations.*;

@RestController
@RequestMapping("/api/members")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "http://localhost:4200")
public class MemberController implements Serializable {

    private final MemberService memberService;
    private final SubscriptionService subscriptionService;

    /**
     * Get all members with optional filters and pagination
     */
    @GetMapping
    @PreAuthorize(CAN_MANAGE_MEMBERS)
    public ResponseEntity<PageResponseDto<MemberResponseDto>> getMembers(
            @RequestParam Long siteId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String memberType,
            @RequestParam(required = false) Long subscriptionId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String sortBy,
            @RequestParam(defaultValue = "ASC") String sortDirection) {

        log.debug("GET /api/members - siteId: {}, status: {}, search: {}, memberType: {}, subscriptionId: {}, page: {}, size: {}",
                  siteId, status, search, memberType, subscriptionId, page, size);

        // Créer l'objet de pagination
        PageRequestDto pageRequest = PageRequestDto.builder()
                .page(page)
                .size(size)
                .sortBy(sortBy)
                .sortDirection(sortDirection)
                .build();
        pageRequest.validate();

        Page<Member> memberPage;

        // Convertir les strings en enums
        MemberStatus statusEnum = null;
        if (status != null && !status.trim().isEmpty()) {
            try {
                statusEnum = MemberStatus.valueOf(status.trim().toUpperCase());
            } catch (IllegalArgumentException e) {
                log.warn("Invalid status value: {}", status);
            }
        }

        MemberType memberTypeEnum = null;
        if (memberType != null && !memberType.trim().isEmpty()) {
            try {
                memberTypeEnum = MemberType.valueOf(memberType.trim().toUpperCase());
            } catch (IllegalArgumentException e) {
                log.warn("Invalid memberType value: {}", memberType);
            }
        }

        // Si un terme de recherche est fourni, utiliser la recherche
        if (search != null && !search.trim().isEmpty()) {
            memberPage = memberService.searchMembers(siteId, search.trim(), statusEnum, memberTypeEnum, subscriptionId, pageRequest.toPageable());
        } else {
            // Logique existante sans recherche mais avec filtres
            memberPage = memberService.getMembersWithFilters(siteId, statusEnum, memberTypeEnum, subscriptionId, pageRequest.toPageable());
        }

        // Récupérer tous les abonnements pour le site pour mapper les noms
        List<Subscription> subscriptions = subscriptionService.getAllSubscriptions(siteId);
        Map<Long, String> subscriptionNames = subscriptions.stream()
                .collect(Collectors.toMap(Subscription::getSubscriptionId, Subscription::getName));

        // Convertir en DTOs avec les noms des abonnements
        Page<MemberResponseDto> memberDtoPage = memberPage.map(member -> {
            String subscriptionName = member.getSubscriptionId() != null ?
                subscriptionNames.get(member.getSubscriptionId()) : null;
            return MemberResponseDto.fromEntity(member, subscriptionName);
        });

        // Créer la réponse paginée
        PageResponseDto<MemberResponseDto> response = PageResponseDto.fromPage(memberDtoPage);

        return ResponseEntity.ok(response);
    }

    /**
     * Get member by ID
     */
    @GetMapping("/{id}")
    @PreAuthorize(CAN_MANAGE_MEMBERS)
    public ResponseEntity<MemberResponseDto> getMemberById(@PathVariable Long id) {
        log.debug("GET /api/members/{}", id);

        Member member = memberService.getMemberById(id);

        // Récupérer le nom de l'abonnement si présent
        String subscriptionName = null;
        if (member.getSubscriptionId() != null) {
            try {
                Subscription subscription = subscriptionService.getSubscriptionById(member.getSubscriptionId());
                subscriptionName = subscription.getName();
            } catch (Exception e) {
                log.warn("Could not find subscription with ID: {}", member.getSubscriptionId());
            }
        }

        MemberResponseDto memberDto = MemberResponseDto.fromEntity(member, subscriptionName);

        return ResponseEntity.ok(memberDto);
    }

    /**
     * Create a new member
     */
    @PostMapping
    @PreAuthorize(CAN_MANAGE_MEMBERS)
    public ResponseEntity<MemberResponseDto> createMember(
            @RequestParam Long siteId,
            @Valid @RequestBody MemberRequestDto memberRequestDto) {
        log.debug("POST /api/members - Creating member for site: {}", siteId);

        Member member = memberRequestDto.toEntity(siteId);
        Member createdMember = memberService.createMember(member);
        MemberResponseDto responseDto = MemberResponseDto.fromEntity(createdMember);

        return ResponseEntity.status(HttpStatus.CREATED).body(responseDto);
    }

    /**
     * Update an existing member
     */
    @PutMapping("/{id}")
    @PreAuthorize(CAN_MANAGE_MEMBERS)
    public ResponseEntity<MemberResponseDto> updateMember(
            @PathVariable Long id,
            @Valid @RequestBody MemberRequestDto memberRequestDto) {
        log.debug("PUT /api/members/{}", id);

        Member member = memberRequestDto.toEntity(null); // siteId will be preserved from existing member
        Member updatedMember = memberService.updateMember(id, member);
        MemberResponseDto responseDto = MemberResponseDto.fromEntity(updatedMember);

        return ResponseEntity.ok(responseDto);
    }

    /**
     * Delete a member
     */
    @DeleteMapping("/{id}")
    @PreAuthorize(CAN_MANAGE_MEMBERS)
    public ResponseEntity<Void> deleteMember(@PathVariable Long id) {
        log.debug("DELETE /api/members/{}", id);

        memberService.deleteMember(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Get members count for a site
     */
    @GetMapping("/count")
    @PreAuthorize(CAN_MANAGE_MEMBERS)
    public ResponseEntity<Long> getMembersCount(@RequestParam Long siteId) {
        log.debug("GET /api/members/count - siteId: {}", siteId);

        long count = memberService.countMembersBySite(siteId);
        return ResponseEntity.ok(count);
    }
}
