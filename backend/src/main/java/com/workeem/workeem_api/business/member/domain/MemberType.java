package com.workeem.workeem_api.business.member.domain;

/**
 * Types de membres disponibles
 */
public enum MemberType {
    
    /**
     * Étudiant
     */
    STUDENT("Étudiant"),
    
    /**
     * Professionnel
     */
    PROFESSIONAL("Professionnel"),
    
    /**
     * Entreprise (pour domiciliation)
     */
    COMPANY("Entreprise");

    private final String displayName;

    MemberType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}
