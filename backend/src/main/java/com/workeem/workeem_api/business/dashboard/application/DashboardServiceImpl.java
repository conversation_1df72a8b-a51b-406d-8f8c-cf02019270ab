package com.workeem.workeem_api.business.dashboard.application;

import com.workeem.workeem_api.business.dashboard.web.dto.DashboardStatsDto;
import com.workeem.workeem_api.business.member.application.MemberService;
import com.workeem.workeem_api.business.space.application.SpaceService;
import com.workeem.workeem_api.business.reservation.application.ReservationService;
import com.workeem.workeem_api.business.subscription.application.SubscriptionService;
import com.workeem.workeem_api.business.site.application.SiteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class DashboardServiceImpl implements DashboardService {

    private final MemberService memberService;
    private final SpaceService spaceService;
    private final ReservationService reservationService;
    private final SubscriptionService subscriptionService;
    private final SiteService siteService;

    @Override
    public DashboardStatsDto getDashboardStats(String siteId) {
        log.debug("Getting dashboard stats for site: {}", siteId);
        
        try {
            // Récupérer le nom du site
            String siteName = siteService.getSiteById(Long.parseLong(siteId)).getName();
            
            // Construire les statistiques complètes
            return DashboardStatsDto.builder()
                    .siteId(siteId)
                    .siteName(siteName)
                    .calculatedAt(System.currentTimeMillis())
                    
                    // Statistiques des membres
                    .totalMembers(memberService.countMembersBySite(Long.parseLong(siteId)))
                    .activeMembers(memberService.countActiveMembersBySite(Long.parseLong(siteId)))
                    
                    // Statistiques des espaces
                    .totalSpaces(spaceService.countSpacesBySite(siteId))
                    .availableSpaces(spaceService.countActiveSpacesBySite(siteId))
                    
                    // Statistiques des réservations
                    .totalReservations(reservationService.countReservationsBySite(Long.parseLong(siteId)))
                    .activeReservations(reservationService.countActiveReservationsBySite(Long.parseLong(siteId)))
                    .upcomingReservations(reservationService.countUpcomingReservationsBySite(Long.parseLong(siteId)))
                    .todayReservations(reservationService.countTodayReservationsBySite(Long.parseLong(siteId)))
                    
                    // Statistiques financières (à 0 pour l'instant, seront calculées selon les permissions)
                    .monthlyRevenue(0.0)
                    .totalRevenue(0.0)
                    .pendingPayments(0.0)
                    .overduePayments(0.0)
                    
                    // Statistiques des abonnements
                    .activeSubscriptions(subscriptionService.countActiveSubscriptionsBySite(Long.parseLong(siteId)))
                    .expiringSubscriptions(0L) // TODO: Implémenter la logique des abonnements qui expirent
                    
                    // Taux d'occupation
                    .occupancyRate(calculateOccupancyRate(siteId))
                    .currentOccupancy(reservationService.getCurrentOccupancyBySite(Long.parseLong(siteId)))
                    .totalCapacity(spaceService.getTotalCapacityBySite(Long.parseLong(siteId)))
                    
                    .build();
                    
        } catch (Exception e) {
            log.error("Error calculating dashboard stats for site {}: {}", siteId, e.getMessage());
            throw new RuntimeException("Failed to calculate dashboard statistics", e);
        }
    }

    @Override
    public DashboardStatsDto.DashboardStatsDtoBuilder getMemberStats(String siteId) {
        return DashboardStatsDto.builder()
                .totalMembers(memberService.countMembersBySite(Long.parseLong(siteId)))
                .activeMembers(memberService.countActiveMembersBySite(Long.parseLong(siteId)));
    }

    @Override
    public DashboardStatsDto.DashboardStatsDtoBuilder getSpaceStats(String siteId) {
        return DashboardStatsDto.builder()
                .totalSpaces(spaceService.countSpacesBySite(siteId))
                .availableSpaces(spaceService.countActiveSpacesBySite(siteId));
    }

    @Override
    public DashboardStatsDto.DashboardStatsDtoBuilder getReservationStats(String siteId) {
        return DashboardStatsDto.builder()
                .totalReservations(reservationService.countReservationsBySite(Long.parseLong(siteId)))
                .activeReservations(reservationService.countActiveReservationsBySite(Long.parseLong(siteId)))
                .upcomingReservations(reservationService.countUpcomingReservationsBySite(Long.parseLong(siteId)))
                .todayReservations(reservationService.countTodayReservationsBySite(Long.parseLong(siteId)));
    }

    @Override
    public DashboardStatsDto.DashboardStatsDtoBuilder getFinancialStats(String siteId) {
        // TODO: Implémenter les statistiques financières réelles
        return DashboardStatsDto.builder()
                .monthlyRevenue(0.0)
                .totalRevenue(0.0)
                .pendingPayments(0.0)
                .overduePayments(0.0);
    }

    @Override
    public DashboardStatsDto.DashboardStatsDtoBuilder getSubscriptionStats(String siteId) {
        return DashboardStatsDto.builder()
                .activeSubscriptions(subscriptionService.countActiveSubscriptionsBySite(Long.parseLong(siteId)))
                .expiringSubscriptions(0L); // TODO: Implémenter
    }

    @Override
    public Double calculateOccupancyRate(String siteId) {
        try {
            Integer currentOccupancy = reservationService.getCurrentOccupancyBySite(Long.parseLong(siteId));
            Integer totalCapacity = spaceService.getTotalCapacityBySite(Long.parseLong(siteId));
            
            if (totalCapacity == null || totalCapacity == 0) {
                return 0.0;
            }
            
            return (currentOccupancy.doubleValue() / totalCapacity.doubleValue()) * 100.0;
        } catch (Exception e) {
            log.error("Error calculating occupancy rate for site {}: {}", siteId, e.getMessage());
            return 0.0;
        }
    }
}
