package com.workeem.workeem_api.business.space.application;

import com.workeem.workeem_api.business.space.domain.Space;
import com.workeem.workeem_api.business.space.domain.SpaceType;
import com.workeem.workeem_api.business.space.web.dto.EquipmentDto;
import com.workeem.workeem_api.business.space.web.dto.SpaceAvailabilityDto;
import com.workeem.workeem_api.business.space.web.dto.SpaceSearchDto;

import java.util.List;

public interface SpaceService {

    /**
     * Get all spaces for a specific site
     */
    List<Space> getAllSpaces(String siteId);

    /**
     * Get all active spaces for a specific site
     */
    List<Space> getActiveSpaces(String siteId);

    /**
     * Get space by ID
     */
    Space getSpaceById(Long id);

    /**
     * Get spaces by type for a specific site
     */
    List<Space> getSpacesByType(String siteId, SpaceType type);

    /**
     * Create a new space
     */
    Space createSpace(Space space);

    /**
     * Create a new space with equipment
     */
    Space createSpace(Space space, List<EquipmentDto> equipmentDtos);

    /**
     * Create a new space with equipment and availability
     */
    Space createSpace(Space space, List<EquipmentDto> equipmentDtos, SpaceAvailabilityDto availabilityDto);

    /**
     * Update an existing space
     */
    Space updateSpace(Long spaceId, Space space);

    /**
     * Update an existing space with equipment
     */
    Space updateSpace(Long spaceId, Space space, List<EquipmentDto> equipmentDtos);

    /**
     * Update an existing space with equipment and availability
     */
    Space updateSpace(Long spaceId, Space space, List<EquipmentDto> equipmentDtos, SpaceAvailabilityDto availabilityDto);

    /**
     * Delete a space by ID
     */
    void deleteSpace(Long id);

    /**
     * Toggle space active status (activate/deactivate)
     */
    Space toggleSpaceActiveStatus(Long spaceId);

    /**
     * Check if space name exists for a site
     */
    boolean existsByName(String siteId, String name);

    /**
     * Count total spaces for a site
     */
    long countSpacesBySite(String siteId);

    /**
     * Count active spaces for a site
     */
    long countActiveSpacesBySite(String siteId);

    /**
     * Get total capacity for a site
     */
    Integer getTotalCapacityBySite(Long siteId);

    /**
     * Get equipment for a space
     */
    List<EquipmentDto> getEquipmentBySpaceId(Long spaceId);

    /**
     * Get availability for a space
     */
    SpaceAvailabilityDto getAvailabilityBySpaceId(Long spaceId);

    /**
     * Update availability for a space
     */
    void updateSpaceAvailability(Long spaceId, SpaceAvailabilityDto availabilityDto);

    /**
     * Search spaces with filters
     */
    List<Space> searchSpaces(SpaceSearchDto searchDto);
}
