package com.workeem.workeem_api.business.dashboard.web.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DashboardStatsDto {
    
    // Statistiques des membres
    private Long totalMembers;
    private Long activeMembers;
    
    // Statistiques des espaces
    private Long totalSpaces;
    private Long availableSpaces;
    
    // Statistiques des réservations
    private Long totalReservations;
    private Long activeReservations;
    private Long upcomingReservations;
    private Long todayReservations;
    
    // Statistiques financières (optionnelles selon les permissions)
    private Double monthlyRevenue;
    private Double totalRevenue;
    private Double pendingPayments;
    private Double overduePayments;
    
    // Statistiques des abonnements
    private Long activeSubscriptions;
    private Long expiringSubscriptions;
    
    // Taux d'occupation
    private Double occupancyRate;
    private Integer currentOccupancy;
    private Integer totalCapacity;
    
    // Métadonnées
    private String siteId;
    private String siteName;
    private Long calculatedAt; // timestamp
}
