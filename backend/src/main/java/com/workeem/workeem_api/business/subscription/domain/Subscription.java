package com.workeem.workeem_api.business.subscription.domain;

import com.workeem.workeem_api.business.member.domain.MemberType;
import com.workeem.workeem_api.shared.common.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Entity
@Table(name = "subscriptions")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class Subscription extends BaseEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "subscription_id")
    private Long subscriptionId;

    @Column(name = "site_id", nullable = false)
    private Long siteId;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "price", nullable = false, precision = 10, scale = 2)
    private BigDecimal price;

    @Column(name = "currency", nullable = false, length = 10)
    private String currency = "MAD";

    @Column(name = "duration_days", nullable = false)
    private Integer durationDays;

    @Enumerated(EnumType.STRING)
    @Column(name = "member_type", length = 20)
    private MemberType memberType;

    @Column(name = "features", columnDefinition = "TEXT")
    private String features;

    @Column(name = "max_reservations_per_day", nullable = false)
    private Integer maxReservationsPerDay = 3;

    @Column(name = "max_reservations_per_week", nullable = false)
    private Integer maxReservationsPerWeek = 15;

    @Column(name = "max_reservations_per_month", nullable = false)
    private Integer maxReservationsPerMonth = 60;

    @Column(name = "max_consecutive_hours", nullable = false)
    private Integer maxConsecutiveHours = 8;

    @Column(name = "advance_booking_days", nullable = false)
    private Integer advanceBookingDays = 30;

    @Column(name = "can_book_meeting_rooms", nullable = false)
    private Boolean canBookMeetingRooms = true;

    @Column(name = "can_access_premium_areas", nullable = false)
    private Boolean canAccessPremiumAreas = false;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    // Méthodes utilitaires
    public String getFormattedPrice() {
        return price + " " + currency;
    }

    public boolean isCompatibleWith(MemberType memberType) {
        return this.memberType == null || this.memberType.equals(memberType);
    }
}
