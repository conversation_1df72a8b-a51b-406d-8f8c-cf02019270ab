package com.workeem.workeem_api.business.space.infrastructure;

import com.workeem.workeem_api.business.space.domain.Equipment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EquipmentRepository extends JpaRepository<Equipment, Long> {
    
    List<Equipment> findBySpaceId(Long spaceId);
    
    @Modifying
    @Query("DELETE FROM Equipment e WHERE e.spaceId = :spaceId")
    void deleteBySpaceId(@Param("spaceId") Long spaceId);
}
