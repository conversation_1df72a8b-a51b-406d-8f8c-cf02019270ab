package com.workeem.workeem_api.business.reservation.web;

import com.workeem.workeem_api.business.reservation.application.ReservationService;
import com.workeem.workeem_api.business.reservation.domain.Reservation;
import com.workeem.workeem_api.business.reservation.domain.ReservationStatus;
import com.workeem.workeem_api.business.reservation.web.dto.ReservationRequestDto;
import com.workeem.workeem_api.business.reservation.web.dto.ReservationResponseDto;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.security.access.prepost.PreAuthorize;
import static com.workeem.workeem_api.shared.security.PlanAuthorizations.*;

@RestController
@RequestMapping("/api/reservations")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "http://localhost:4200")
public class ReservationController implements Serializable {

    private final ReservationService reservationService;

    /**
     * Get all reservations with optional filters
     */
    @GetMapping
    @PreAuthorize(CAN_MANAGE_RESERVATIONS)
    public ResponseEntity<List<ReservationResponseDto>> getReservations(
            @RequestParam Long siteId,
            @RequestParam(required = false) Long spaceId,
            @RequestParam(required = false) ReservationStatus status,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        log.debug("GET /api/reservations - siteId: {}, spaceId: {}, status: {}, dateRange: {} to {}", 
                  siteId, spaceId, status, startDate, endDate);

        List<Reservation> reservations = reservationService.getReservationsWithFilters(
                siteId, spaceId, status, startDate, endDate);

        List<ReservationResponseDto> reservationDtos = reservations.stream()
                .map(ReservationResponseDto::fromEntity)
                .collect(Collectors.toList());

        return ResponseEntity.ok(reservationDtos);
    }

    /**
     * Get reservation by ID
     */
    @GetMapping("/{id}")
    @PreAuthorize(CAN_MANAGE_RESERVATIONS)
    public ResponseEntity<ReservationResponseDto> getReservationById(@PathVariable Long id) {
        log.debug("GET /api/reservations/{}", id);

        Reservation reservation = reservationService.getReservationById(id);
        ReservationResponseDto reservationDto = ReservationResponseDto.fromEntity(reservation);
        
        return ResponseEntity.ok(reservationDto);
    }

    /**
     * Get reservations by space
     */
    @GetMapping("/by-space/{spaceId}")
    @PreAuthorize(CAN_MANAGE_RESERVATIONS)
    public ResponseEntity<List<ReservationResponseDto>> getReservationsBySpace(
            @PathVariable Long spaceId,
            @RequestParam Long siteId) {
        log.debug("GET /api/reservations/by-space/{} - siteId: {}", spaceId, siteId);

        List<Reservation> reservations = reservationService.getReservationsBySpace(siteId, spaceId);
        List<ReservationResponseDto> reservationDtos = reservations.stream()
                .map(ReservationResponseDto::fromEntity)
                .collect(Collectors.toList());

        return ResponseEntity.ok(reservationDtos);
    }

    /**
     * Get reservations by user
     */
    @GetMapping("/by-user/{userId}")
    public ResponseEntity<List<ReservationResponseDto>> getReservationsByUser(
            @PathVariable String userId,
            @RequestParam Long siteId) {
        log.debug("GET /api/reservations/by-user/{} - siteId: {}", userId, siteId);

        List<Reservation> reservations = reservationService.getReservationsByUser(siteId, userId);
        List<ReservationResponseDto> reservationDtos = reservations.stream()
                .map(ReservationResponseDto::fromEntity)
                .collect(Collectors.toList());

        return ResponseEntity.ok(reservationDtos);
    }

    /**
     * Create a new reservation
     */
    @PostMapping
    public ResponseEntity<ReservationResponseDto> createReservation(
            @RequestParam Long siteId,
            @Valid @RequestBody ReservationRequestDto reservationRequestDto) {
        log.debug("POST /api/reservations - Creating reservation for space: {} in site: {}", 
                  reservationRequestDto.getSpaceId(), siteId);

        Reservation reservation = reservationRequestDto.toEntity(siteId);
        Reservation createdReservation = reservationService.createReservationWithMemberInfo(
                reservation,
                reservationRequestDto.getSpaceId(),
                reservationRequestDto.getMemberId(),
                reservationRequestDto.getFirstName(),
                reservationRequestDto.getLastName(),
                reservationRequestDto.getEmail(),
                reservationRequestDto.getPhone(),
                reservationRequestDto.getCompany());
        ReservationResponseDto responseDto = ReservationResponseDto.fromEntity(createdReservation);

        return ResponseEntity.status(HttpStatus.CREATED).body(responseDto);
    }

    /**
     * Update an existing reservation
     */
    @PutMapping("/{id}")
    public ResponseEntity<ReservationResponseDto> updateReservation(
            @PathVariable Long id,
            @RequestParam Long siteId,
            @Valid @RequestBody ReservationRequestDto reservationRequestDto) {
        log.debug("PUT /api/reservations/{} - Updating reservation for site: {}", id, siteId);

        Reservation reservation = reservationRequestDto.toEntity(siteId);
        Reservation updatedReservation = reservationService.updateReservation(
                id, reservation, reservationRequestDto.getSpaceId(), reservationRequestDto.getMemberId());
        ReservationResponseDto responseDto = ReservationResponseDto.fromEntity(updatedReservation);

        return ResponseEntity.ok(responseDto);
    }

    /**
     * Cancel a reservation
     */
    @PatchMapping("/{id}/cancel")
    public ResponseEntity<ReservationResponseDto> cancelReservation(@PathVariable Long id) {
        log.debug("PATCH /api/reservations/{}/cancel", id);

        Reservation reservation = reservationService.cancelReservation(id);
        ReservationResponseDto responseDto = ReservationResponseDto.fromEntity(reservation);

        return ResponseEntity.ok(responseDto);
    }

    /**
     * Update reservation status
     */
    @PatchMapping("/{id}/status")
    public ResponseEntity<ReservationResponseDto> updateReservationStatus(
            @PathVariable Long id,
            @RequestParam ReservationStatus status) {
        log.debug("PATCH /api/reservations/{}/status - status: {}", id, status);

        Reservation reservation = reservationService.updateReservationStatus(id, status);
        ReservationResponseDto responseDto = ReservationResponseDto.fromEntity(reservation);

        return ResponseEntity.ok(responseDto);
    }

    /**
     * Delete a reservation
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteReservation(@PathVariable Long id) {
        log.debug("DELETE /api/reservations/{}", id);

        reservationService.deleteReservation(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Get reservations count for a site
     */
    @GetMapping("/count")
    public ResponseEntity<Long> getReservationsCount(@RequestParam Long siteId) {
        log.debug("GET /api/reservations/count - siteId: {}", siteId);

        long count = reservationService.countReservationsBySite(siteId);
        return ResponseEntity.ok(count);
    }

    /**
     * Check for conflicting reservations
     */
    @GetMapping("/check-conflicts")
    public ResponseEntity<Boolean> checkConflicts(
            @RequestParam Long siteId,
            @RequestParam Long spaceId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(required = false) Long excludeReservationId) {
        
        log.debug("GET /api/reservations/check-conflicts - siteId: {}, spaceId: {}, timeRange: {} to {}", 
                  siteId, spaceId, startTime, endTime);

        boolean hasConflicts;
        if (excludeReservationId != null) {
            hasConflicts = reservationService.hasConflictingReservations(siteId, spaceId, startTime, endTime, excludeReservationId);
        } else {
            hasConflicts = reservationService.hasConflictingReservations(siteId, spaceId, startTime, endTime);
        }

        return ResponseEntity.ok(hasConflicts);
    }
}
