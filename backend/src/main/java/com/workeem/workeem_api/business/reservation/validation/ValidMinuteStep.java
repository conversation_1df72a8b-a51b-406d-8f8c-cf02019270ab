package com.workeem.workeem_api.business.reservation.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = ValidMinuteStepValidator.class)
@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidMinuteStep {
    String message() default "Les minutes doivent être des multiples de 15 (00, 15, 30, 45)";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
    int step() default 15;
}
