package com.workeem.workeem_api.business.space.web;

import com.workeem.workeem_api.business.space.application.SpaceService;
import com.workeem.workeem_api.business.space.domain.Floor;
import com.workeem.workeem_api.business.space.domain.Space;
import com.workeem.workeem_api.business.space.domain.SpaceType;
import com.workeem.workeem_api.business.space.web.dto.EquipmentDto;
import com.workeem.workeem_api.business.space.web.dto.SpaceAvailabilityDto;
import com.workeem.workeem_api.business.space.web.dto.SpaceRequestDto;
import com.workeem.workeem_api.business.space.web.dto.SpaceResponseDto;
import com.workeem.workeem_api.business.space.web.dto.SpaceSearchDto;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.security.access.prepost.PreAuthorize;
import static com.workeem.workeem_api.shared.security.PlanAuthorizations.*;

@RestController
@RequestMapping("/api/spaces")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "http://localhost:4200")
public class SpaceController implements Serializable {

    private final SpaceService spaceService;

    /**
     * Get all spaces for a specific site
     */
    @GetMapping
    @PreAuthorize(CAN_MANAGE_SPACES)
    public ResponseEntity<List<SpaceResponseDto>> getAllSpaces(
            @RequestParam String siteId,
            @RequestParam(required = false) Boolean activeOnly) {
        log.debug("GET /api/spaces - siteId: {}, activeOnly: {}", siteId, activeOnly);

        List<Space> spaces;
        if (Boolean.TRUE.equals(activeOnly)) {
            spaces = spaceService.getActiveSpaces(siteId);
        } else {
            spaces = spaceService.getAllSpaces(siteId);
        }

        List<SpaceResponseDto> spaceDtos = spaces.stream()
                .map(space -> {
                    List<EquipmentDto> equipment = spaceService.getEquipmentBySpaceId(space.getSpaceId());
                    SpaceAvailabilityDto availability = spaceService.getAvailabilityBySpaceId(space.getSpaceId());
                    return SpaceResponseDto.fromEntity(space, equipment, availability);
                })
                .collect(Collectors.toList());

        return ResponseEntity.ok(spaceDtos);
    }

    /**
     * Get space by ID
     */
    @GetMapping("/{id}")
    @PreAuthorize(CAN_MANAGE_SPACES)
    public ResponseEntity<SpaceResponseDto> getSpaceById(@PathVariable Long id) {
        log.debug("GET /api/spaces/{}", id);
        
        Space space = spaceService.getSpaceById(id);
        List<EquipmentDto> equipment = spaceService.getEquipmentBySpaceId(space.getSpaceId());
        SpaceAvailabilityDto availability = spaceService.getAvailabilityBySpaceId(space.getSpaceId());
        SpaceResponseDto spaceDto = SpaceResponseDto.fromEntity(space, equipment, availability);
        return ResponseEntity.ok(spaceDto);
    }

    /**
     * Get spaces by type for a specific site
     */
    @GetMapping("/by-type")
    @PreAuthorize(CAN_MANAGE_SPACES)
    public ResponseEntity<List<SpaceResponseDto>> getSpacesByType(
            @RequestParam String siteId,
            @RequestParam SpaceType type) {
        log.debug("GET /api/spaces/by-type - siteId: {}, type: {}", siteId, type);

        List<Space> spaces = spaceService.getSpacesByType(siteId, type);
        List<SpaceResponseDto> spaceDtos = spaces.stream()
                .map(space -> {
                    List<EquipmentDto> equipment = spaceService.getEquipmentBySpaceId(space.getSpaceId());
                    SpaceAvailabilityDto availability = spaceService.getAvailabilityBySpaceId(space.getSpaceId());
                    return SpaceResponseDto.fromEntity(space, equipment, availability);
                })
                .collect(Collectors.toList());

        return ResponseEntity.ok(spaceDtos);
    }

    /**
     * Search spaces with filters
     */
    @GetMapping("/search")
    @PreAuthorize(CAN_MANAGE_SPACES)
    public ResponseEntity<List<SpaceResponseDto>> searchSpaces(
            @RequestParam String siteId,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) SpaceType type,
            @RequestParam(required = false) String location,
            @RequestParam(required = false) Floor floor,
            @RequestParam(required = false) Integer minCapacity,
            @RequestParam(required = false) Boolean activeOnly) {

        log.debug("GET /api/spaces/search - siteId: {}, search: {}, type: {}, location: {}, floor: {}, minCapacity: {}, activeOnly: {}",
                  siteId, search, type, location, floor, minCapacity, activeOnly);

        SpaceSearchDto searchDto = SpaceSearchDto.builder()
                .siteId(siteId)
                .search(search)
                .type(type)
                .location(location)
                .floor(floor)
                .minCapacity(minCapacity)
                .activeOnly(activeOnly)
                .build();

        List<Space> spaces = spaceService.searchSpaces(searchDto);
        List<SpaceResponseDto> spaceDtos = spaces.stream()
                .map(space -> {
                    List<EquipmentDto> equipment = spaceService.getEquipmentBySpaceId(space.getSpaceId());
                    SpaceAvailabilityDto availability = spaceService.getAvailabilityBySpaceId(space.getSpaceId());
                    return SpaceResponseDto.fromEntity(space, equipment, availability);
                })
                .collect(Collectors.toList());

        return ResponseEntity.ok(spaceDtos);
    }

    /**
     * Create a new space
     */
    @PostMapping
    @PreAuthorize(CAN_MANAGE_SPACES)
    public ResponseEntity<SpaceResponseDto> createSpace(
            @RequestParam Long siteId,
            @Valid @RequestBody SpaceRequestDto spaceRequestDto) {
        log.debug("POST /api/spaces - Creating space: {} for site: {}", spaceRequestDto.getName(), siteId);
        
        Space space = spaceRequestDto.toEntity(siteId);
        Space createdSpace = spaceService.createSpace(space, spaceRequestDto.getEquipmentDtos(), spaceRequestDto.getAvailabilityDto());
        List<EquipmentDto> equipment = spaceService.getEquipmentBySpaceId(createdSpace.getSpaceId());
        SpaceAvailabilityDto availability = spaceService.getAvailabilityBySpaceId(createdSpace.getSpaceId());
        SpaceResponseDto responseDto = SpaceResponseDto.fromEntity(createdSpace, equipment, availability);
        
        return ResponseEntity.status(HttpStatus.CREATED).body(responseDto);
    }

    /**
     * Update an existing space
     */
    @PutMapping("/{id}")
    @PreAuthorize(CAN_MANAGE_SPACES)
    public ResponseEntity<SpaceResponseDto> updateSpace(
            @PathVariable Long id,
            @RequestParam Long siteId,
            @Valid @RequestBody SpaceRequestDto spaceRequestDto) {
        log.debug("PUT /api/spaces/{} - Updating space for site: {}", id, siteId);
        
        Space space = spaceRequestDto.toEntity(siteId);
        Space updatedSpace = spaceService.updateSpace(id, space, spaceRequestDto.getEquipmentDtos(), spaceRequestDto.getAvailabilityDto());
        List<EquipmentDto> equipment = spaceService.getEquipmentBySpaceId(updatedSpace.getSpaceId());
        SpaceAvailabilityDto availability = spaceService.getAvailabilityBySpaceId(updatedSpace.getSpaceId());
        SpaceResponseDto responseDto = SpaceResponseDto.fromEntity(updatedSpace, equipment, availability);
        
        return ResponseEntity.ok(responseDto);
    }

    /**
     * Delete a space
     */
    @DeleteMapping("/{id}")
    @PreAuthorize(CAN_MANAGE_SPACES)
    public ResponseEntity<Void> deleteSpace(@PathVariable Long id) {
        log.debug("DELETE /api/spaces/{}", id);
        
        spaceService.deleteSpace(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Toggle space active status (activate/deactivate)
     */
    @PatchMapping("/{id}/toggle-active")
    @PreAuthorize(CAN_MANAGE_SPACES)
    public ResponseEntity<SpaceResponseDto> toggleSpaceActiveStatus(@PathVariable Long id) {
        log.debug("PATCH /api/spaces/{}/toggle-active", id);

        Space space = spaceService.toggleSpaceActiveStatus(id);
        List<EquipmentDto> equipment = spaceService.getEquipmentBySpaceId(space.getSpaceId());
        SpaceAvailabilityDto availability = spaceService.getAvailabilityBySpaceId(space.getSpaceId());
        SpaceResponseDto responseDto = SpaceResponseDto.fromEntity(space, equipment, availability);

        return ResponseEntity.ok(responseDto);
    }

    /**
     * Get spaces count for a site
     */
    @GetMapping("/count")
    @PreAuthorize(CAN_MANAGE_SPACES)
    public ResponseEntity<Long> getSpacesCount(@RequestParam String siteId) {
        log.debug("GET /api/spaces/count - siteId: {}", siteId);

        long count = spaceService.countSpacesBySite(siteId);
        return ResponseEntity.ok(count);
    }

    /**
     * Get active spaces count for a site
     */
    @GetMapping("/active-count")
    @PreAuthorize(CAN_MANAGE_SPACES)
    public ResponseEntity<Long> getActiveSpacesCount(@RequestParam String siteId) {
        log.debug("GET /api/spaces/active-count - siteId: {}", siteId);

        long count = spaceService.countActiveSpacesBySite(siteId);
        return ResponseEntity.ok(count);
    }
}
