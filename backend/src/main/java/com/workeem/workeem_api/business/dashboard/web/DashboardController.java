package com.workeem.workeem_api.business.dashboard.web;

import com.workeem.workeem_api.business.dashboard.application.DashboardService;
import com.workeem.workeem_api.business.dashboard.web.dto.DashboardStatsDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;

import static com.workeem.workeem_api.shared.security.PlanAuthorizations.ALL_PLANS;

@RestController
@RequestMapping("/api/dashboard")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "http://localhost:4200")
public class DashboardController implements Serializable {

    private final DashboardService dashboardService;

    /**
     * Get complete dashboard statistics for a site
     */
    @GetMapping("/stats")
    @PreAuthorize(ALL_PLANS)
    public ResponseEntity<DashboardStatsDto> getDashboardStats(@RequestParam String siteId) {
        log.debug("GET /api/dashboard/stats - siteId: {}", siteId);

        DashboardStatsDto stats = dashboardService.getDashboardStats(siteId);
        return ResponseEntity.ok(stats);
    }

    /**
     * Get member statistics for a site
     */
    @GetMapping("/stats/members")
    @PreAuthorize(ALL_PLANS)
    public ResponseEntity<DashboardStatsDto> getMemberStats(@RequestParam String siteId) {
        log.debug("GET /api/dashboard/stats/members - siteId: {}", siteId);

        DashboardStatsDto stats = dashboardService.getMemberStats(siteId).build();
        return ResponseEntity.ok(stats);
    }

    /**
     * Get space statistics for a site
     */
    @GetMapping("/stats/spaces")
    @PreAuthorize(ALL_PLANS)
    public ResponseEntity<DashboardStatsDto> getSpaceStats(@RequestParam String siteId) {
        log.debug("GET /api/dashboard/stats/spaces - siteId: {}", siteId);

        DashboardStatsDto stats = dashboardService.getSpaceStats(siteId).build();
        return ResponseEntity.ok(stats);
    }

    /**
     * Get reservation statistics for a site
     */
    @GetMapping("/stats/reservations")
    @PreAuthorize(ALL_PLANS)
    public ResponseEntity<DashboardStatsDto> getReservationStats(@RequestParam String siteId) {
        log.debug("GET /api/dashboard/stats/reservations - siteId: {}", siteId);

        DashboardStatsDto stats = dashboardService.getReservationStats(siteId).build();
        return ResponseEntity.ok(stats);
    }

    /**
     * Get financial statistics for a site
     */
    @GetMapping("/stats/financial")
    @PreAuthorize(ALL_PLANS) // Les permissions financières seront vérifiées dans le service
    public ResponseEntity<DashboardStatsDto> getFinancialStats(@RequestParam String siteId) {
        log.debug("GET /api/dashboard/stats/financial - siteId: {}", siteId);

        DashboardStatsDto stats = dashboardService.getFinancialStats(siteId).build();
        return ResponseEntity.ok(stats);
    }

    /**
     * Get subscription statistics for a site
     */
    @GetMapping("/stats/subscriptions")
    @PreAuthorize(ALL_PLANS)
    public ResponseEntity<DashboardStatsDto> getSubscriptionStats(@RequestParam String siteId) {
        log.debug("GET /api/dashboard/stats/subscriptions - siteId: {}", siteId);

        DashboardStatsDto stats = dashboardService.getSubscriptionStats(siteId).build();
        return ResponseEntity.ok(stats);
    }

    /**
     * Get occupancy rate for a site
     */
    @GetMapping("/stats/occupancy")
    @PreAuthorize(ALL_PLANS)
    public ResponseEntity<Double> getOccupancyRate(@RequestParam String siteId) {
        log.debug("GET /api/dashboard/stats/occupancy - siteId: {}", siteId);

        Double occupancyRate = dashboardService.calculateOccupancyRate(siteId);
        return ResponseEntity.ok(occupancyRate);
    }
}
