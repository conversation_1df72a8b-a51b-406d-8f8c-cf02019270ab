package com.workeem.workeem_api.business.space.web.dto;

import com.workeem.workeem_api.business.space.domain.Floor;
import com.workeem.workeem_api.business.space.domain.SpaceType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO pour les paramètres de recherche d'espaces
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpaceSearchDto {

    private String siteId;
    private SpaceType type;
    private String location;
    private Floor floor;
    private Integer minCapacity;
    private String search; // Recherche textuelle sur nom, description et localisation
    private Boolean activeOnly;
}
