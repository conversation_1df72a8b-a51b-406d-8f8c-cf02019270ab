package com.workeem.workeem_api.business.space.infrastructure;

import com.workeem.workeem_api.business.space.domain.SpaceAvailability;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SpaceAvailabilityRepository extends JpaRepository<SpaceAvailability, Long> {
    
    Optional<SpaceAvailability> findBySpaceId(Long spaceId);
    
    @Modifying
    @Query("DELETE FROM SpaceAvailability sa WHERE sa.spaceId = :spaceId")
    void deleteBySpaceId(@Param("spaceId") Long spaceId);
}
