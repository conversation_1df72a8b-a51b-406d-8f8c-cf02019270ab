package com.workeem.workeem_api.business.dashboard.application;

import com.workeem.workeem_api.business.dashboard.web.dto.DashboardStatsDto;

public interface DashboardService {

    /**
     * Get complete dashboard statistics for a site
     */
    DashboardStatsDto getDashboardStats(String siteId);

    /**
     * Get member statistics for a site
     */
    DashboardStatsDto.DashboardStatsDtoBuilder getMemberStats(String siteId);

    /**
     * Get space statistics for a site
     */
    DashboardStatsDto.DashboardStatsDtoBuilder getSpaceStats(String siteId);

    /**
     * Get reservation statistics for a site
     */
    DashboardStatsDto.DashboardStatsDtoBuilder getReservationStats(String siteId);

    /**
     * Get financial statistics for a site (if user has permissions)
     */
    DashboardStatsDto.DashboardStatsDtoBuilder getFinancialStats(String siteId);

    /**
     * Get subscription statistics for a site
     */
    DashboardStatsDto.DashboardStatsDtoBuilder getSubscriptionStats(String siteId);

    /**
     * Calculate occupancy rate for a site
     */
    Double calculateOccupancyRate(String siteId);
}
