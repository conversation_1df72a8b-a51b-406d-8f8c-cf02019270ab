package com.workeem.workeem_api.business.subscription.web.dto;

import com.workeem.workeem_api.business.member.domain.MemberType;
import com.workeem.workeem_api.business.subscription.domain.Subscription;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionRequestDto {

    @NotBlank(message = "Name is required")
    private String name;

    private String description;

    @NotNull(message = "Price is required")
    @Positive(message = "Price must be positive")
    private BigDecimal price;

    private String currency = "MAD";

    @NotNull(message = "Duration in days is required")
    @Positive(message = "Duration must be positive")
    private Integer durationDays;

    private MemberType memberType;

    private String features;

    @Positive(message = "Max reservations per day must be positive")
    private Integer maxReservationsPerDay = 3;

    @Positive(message = "Max reservations per week must be positive")
    private Integer maxReservationsPerWeek = 15;

    @Positive(message = "Max reservations per month must be positive")
    private Integer maxReservationsPerMonth = 60;

    @Positive(message = "Max consecutive hours must be positive")
    private Integer maxConsecutiveHours = 8;

    @Positive(message = "Advance booking days must be positive")
    private Integer advanceBookingDays = 30;

    private Boolean canBookMeetingRooms = true;

    private Boolean canAccessPremiumAreas = false;

    private Boolean isActive = true;

    /**
     * Convert DTO to Entity
     */
    public Subscription toEntity(Long siteId) {
        Subscription subscription = new Subscription();
        subscription.setSiteId(siteId);
        subscription.setName(this.name);
        subscription.setDescription(this.description);
        subscription.setPrice(this.price);
        subscription.setCurrency(this.currency != null ? this.currency : "MAD");
        subscription.setDurationDays(this.durationDays);
        subscription.setMemberType(this.memberType);
        subscription.setFeatures(this.features);
        subscription.setMaxReservationsPerDay(this.maxReservationsPerDay != null ? this.maxReservationsPerDay : 3);
        subscription.setMaxReservationsPerWeek(this.maxReservationsPerWeek != null ? this.maxReservationsPerWeek : 15);
        subscription.setMaxReservationsPerMonth(this.maxReservationsPerMonth != null ? this.maxReservationsPerMonth : 60);
        subscription.setMaxConsecutiveHours(this.maxConsecutiveHours != null ? this.maxConsecutiveHours : 8);
        subscription.setAdvanceBookingDays(this.advanceBookingDays != null ? this.advanceBookingDays : 30);
        subscription.setCanBookMeetingRooms(this.canBookMeetingRooms != null ? this.canBookMeetingRooms : true);
        subscription.setCanAccessPremiumAreas(this.canAccessPremiumAreas != null ? this.canAccessPremiumAreas : false);
        subscription.setIsActive(this.isActive != null ? this.isActive : true);
        return subscription;
    }
}
