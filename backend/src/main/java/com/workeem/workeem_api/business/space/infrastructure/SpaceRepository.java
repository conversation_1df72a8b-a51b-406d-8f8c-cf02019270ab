package com.workeem.workeem_api.business.space.infrastructure;

import com.workeem.workeem_api.business.space.domain.Floor;
import com.workeem.workeem_api.business.space.domain.Space;
import com.workeem.workeem_api.business.space.domain.SpaceStatus;
import com.workeem.workeem_api.business.space.domain.SpaceType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SpaceRepository extends JpaRepository<Space, Long> {
    
    /**
     * Find all spaces for a specific site
     */
    List<Space> findBySiteId(Long siteId);

    /**
     * Find active spaces for a specific site
     */
    List<Space> findBySiteIdAndIsActiveTrue(Long siteId);

    /**
     * Find spaces by type for a specific site
     */
    List<Space> findBySiteIdAndType(Long siteId, SpaceType type);

    /**
     * Find spaces by type and active status for a specific site
     */
    List<Space> findBySiteIdAndTypeAndIsActiveTrue(Long siteId, SpaceType type);

    /**
     * Check if space name exists for a site (for validation)
     */
    boolean existsBySiteIdAndNameIgnoreCase(Long siteId, String name);

    /**
     * Count total spaces for a site
     */
    long countBySiteId(Long siteId);

    /**
     * Count active spaces for a site
     */
    long countBySiteIdAndIsActiveTrue(Long siteId);



    /**
     * Get total capacity for a site
     */
    @Query("SELECT COALESCE(SUM(s.capacity), 0) FROM Space s WHERE s.siteId = :siteId AND s.isActive = true")
    Integer getTotalCapacityBySite(@Param("siteId") Long siteId);

    /**
     * Search spaces with filters
     */
    @Query("SELECT s FROM Space s WHERE s.siteId = :siteId " +
           "AND (:search IS NULL OR :search = '' OR " +
           "     (s.name IS NOT NULL AND LOWER(s.name) LIKE LOWER(CONCAT('%', :search, '%'))) OR " +
           "     (s.description IS NOT NULL AND LOWER(s.description) LIKE LOWER(CONCAT('%', :search, '%'))) OR " +
           "     (s.location IS NOT NULL AND LOWER(s.location) LIKE LOWER(CONCAT('%', :search, '%')))) " +
           "AND (:type IS NULL OR s.type = :type) " +
           "AND (:location IS NULL OR :location = '' OR " +
           "     (s.location IS NOT NULL AND LOWER(s.location) LIKE LOWER(CONCAT('%', :location, '%')))) " +
           "AND (:floor IS NULL OR s.floor = :floor) " +
           "AND (:minCapacity IS NULL OR s.capacity >= :minCapacity) " +
           "AND (:activeOnly IS NULL OR :activeOnly = false OR s.isActive = true)")
    List<Space> searchSpaces(@Param("siteId") Long siteId,
                            @Param("search") String search,
                            @Param("type") SpaceType type,
                            @Param("location") String location,
                            @Param("floor") Floor floor,
                            @Param("minCapacity") Integer minCapacity,
                            @Param("activeOnly") Boolean activeOnly);
}
