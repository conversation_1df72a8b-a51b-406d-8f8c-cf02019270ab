package com.workeem.workeem_api.business.member.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.workeem.workeem_api.business.member.domain.Member;
import com.workeem.workeem_api.business.member.domain.MemberType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberResponseDto {

    private Long memberId;
    private Long siteId;
    private String firstName;
    private String lastName;
    private String email;
    private String phone;
    private String company;
    private MemberType memberType;
    private String studentCode;
    private String iceNumber;
    private Long subscriptionId;
    private String subscriptionName; // Nom de l'abonnement
    private Boolean isActive;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * Convert Entity to DTO
     */
    public static MemberResponseDto fromEntity(Member member) {
        return fromEntity(member, null);
    }

    /**
     * Convert Entity to DTO with subscription name
     */
    public static MemberResponseDto fromEntity(Member member, String subscriptionName) {
        return MemberResponseDto.builder()
                .memberId(member.getMemberId())
                .siteId(member.getSiteId())
                .firstName(member.getFirstName())
                .lastName(member.getLastName())
                .email(member.getEmail())
                .phone(member.getPhone())
                .company(member.getCompany())
                .memberType(member.getMemberType())
                .studentCode(member.getStudentCode())
                .iceNumber(member.getIceNumber())
                .subscriptionId(member.getSubscriptionId())
                .subscriptionName(subscriptionName)
                .isActive(member.getIsActive())
                .createdAt(member.getCreatedDate())
                .updatedAt(member.getLastModifiedDate())
                .build();
    }
}
