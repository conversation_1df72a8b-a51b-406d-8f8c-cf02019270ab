package com.workeem.workeem_api.business.reservation.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.workeem.workeem_api.business.reservation.domain.Reservation;
import com.workeem.workeem_api.business.reservation.domain.ReservationStatus;
import com.workeem.workeem_api.business.reservation.domain.RecurrenceType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReservationResponseDto {

    private Long id;
    private Long siteId;
    private Long spaceId;
    private String spaceName;
    private Long memberId;
    private String memberName;
    private String memberEmail;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime endTime;

    private String purpose;
    private ReservationStatus status;
    private String statusDisplayName;
    private Integer numberOfPeople;
    private String notes;
    private RecurrenceType recurrence;
    private String recurrenceDisplayName;
    private Double totalCost;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * Convert Entity to DTO
     */
    public static ReservationResponseDto fromEntity(Reservation reservation) {
        return ReservationResponseDto.builder()
                .id(reservation.getReservationId())
                .siteId(reservation.getSiteId())
                .spaceId(reservation.getSpace() != null ? reservation.getSpace().getSpaceId() : null)
                .spaceName(reservation.getSpace() != null ? reservation.getSpace().getName() : null)
                .memberId(reservation.getMember() != null ? reservation.getMember().getMemberId() : null)
                .memberName(reservation.getMember() != null ? reservation.getMember().getFullName() : null)
                .memberEmail(reservation.getMember() != null ? reservation.getMember().getEmail() : null)
                .startTime(reservation.getStartTime())
                .endTime(reservation.getEndTime())
                .purpose(reservation.getPurpose())
                .status(reservation.getStatus())
                .statusDisplayName(reservation.getStatus() != null ? reservation.getStatus().getDisplayName() : null)
                .numberOfPeople(reservation.getNumberOfPeople())
                .notes(reservation.getNotes())
                .recurrence(reservation.getRecurrence())
                .recurrenceDisplayName(reservation.getRecurrence() != null ? reservation.getRecurrence().getDisplayName() : null)
                .totalCost(reservation.getTotalCost())
                .createdAt(reservation.getCreatedDate())
                .updatedAt(reservation.getLastModifiedDate())
                .build();
    }
}
