package com.workeem.workeem_api.business.reservation.application;

import com.workeem.workeem_api.business.reservation.domain.Reservation;
import com.workeem.workeem_api.business.reservation.domain.ReservationStatus;

import java.time.LocalDateTime;
import java.util.List;

public interface ReservationService {

    /**
     * Get all reservations for a specific site
     */
    List<Reservation> getAllReservations(Long siteId);

    /**
     * Get reservations with filters
     */
    List<Reservation> getReservationsWithFilters(Long siteId, Long spaceId, ReservationStatus status,
                                                  LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Get reservation by ID
     */
    Reservation getReservationById(Long id);

    /**
     * Get reservations by space
     */
    List<Reservation> getReservationsBySpace(Long siteId, Long spaceId);

    /**
     * Get reservations by user
     */
    List<Reservation> getReservationsByUser(Long siteId, String userId);

    /**
     * Get reservations in date range
     */
    List<Reservation> getReservationsInDateRange(Long siteId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Create a new reservation
     */
    Reservation createReservation(Reservation reservation);

    /**
     * Create a new reservation with Space and Member IDs
     */
    Reservation createReservation(Reservation reservation, Long spaceId, Long memberId);

    /**
     * Create a new reservation with automatic member creation if needed
     */
    Reservation createReservationWithMemberInfo(Reservation reservation, Long spaceId, Long memberId,
                                                String firstName, String lastName, String email,
                                                String phone, String company);

    /**
     * Update an existing reservation
     */
    Reservation updateReservation(Long reservationId, Reservation reservation);

    /**
     * Update an existing reservation with Space and Member IDs
     */
    Reservation updateReservation(Long reservationId, Reservation reservation, Long spaceId, Long memberId);

    /**
     * Cancel a reservation
     */
    Reservation cancelReservation(Long reservationId);

    /**
     * Update reservation status
     */
    Reservation updateReservationStatus(Long reservationId, ReservationStatus status);

    /**
     * Delete a reservation
     */
    void deleteReservation(Long id);

    /**
     * Check for conflicting reservations
     */
    boolean hasConflictingReservations(Long siteId, Long spaceId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Check for conflicting reservations excluding a specific reservation
     */
    boolean hasConflictingReservations(Long siteId, Long spaceId, LocalDateTime startTime, LocalDateTime endTime, Long excludeReservationId);

    /**
     * Count reservations by site
     */
    long countReservationsBySite(Long siteId);

    /**
     * Count reservations by status
     */
    long countReservationsByStatus(Long siteId, ReservationStatus status);

    /**
     * Count active reservations by site
     */
    long countActiveReservationsBySite(Long siteId);

    /**
     * Count upcoming reservations by site
     */
    long countUpcomingReservationsBySite(Long siteId);

    /**
     * Count today's reservations by site
     */
    long countTodayReservationsBySite(Long siteId);

    /**
     * Get current occupancy by site
     */
    Integer getCurrentOccupancyBySite(Long siteId);

    /**
     * Check if a space has an active reservation at a specific time
     */
    boolean hasActiveReservationForSpace(Long siteId, Long spaceId, LocalDateTime dateTime);

    /**
     * Get current occupancy for a specific space (number of people currently in the space)
     */
    Integer getCurrentOccupancyForSpace(Long siteId, Long spaceId, LocalDateTime dateTime);
}
