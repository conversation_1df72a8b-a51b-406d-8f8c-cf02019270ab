package com.workeem.workeem_api.business.subscription.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.workeem.workeem_api.business.member.domain.MemberType;
import com.workeem.workeem_api.business.subscription.domain.Subscription;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionResponseDto {

    private Long subscriptionId;
    private Long siteId;
    private String name;
    private String description;
    private BigDecimal price;
    private String currency;
    private Integer durationDays;
    private MemberType memberType;
    private String features;
    private Integer maxReservationsPerDay;
    private Integer maxReservationsPerWeek;
    private Integer maxReservationsPerMonth;
    private Integer maxConsecutiveHours;
    private Integer advanceBookingDays;
    private Boolean canBookMeetingRooms;
    private Boolean canAccessPremiumAreas;
    private Boolean isActive;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * Convert Entity to DTO
     */
    public static SubscriptionResponseDto fromEntity(Subscription subscription) {
        return SubscriptionResponseDto.builder()
                .subscriptionId(subscription.getSubscriptionId())
                .siteId(subscription.getSiteId())
                .name(subscription.getName())
                .description(subscription.getDescription())
                .price(subscription.getPrice())
                .currency(subscription.getCurrency())
                .durationDays(subscription.getDurationDays())
                .memberType(subscription.getMemberType())
                .features(subscription.getFeatures())
                .maxReservationsPerDay(subscription.getMaxReservationsPerDay())
                .maxReservationsPerWeek(subscription.getMaxReservationsPerWeek())
                .maxReservationsPerMonth(subscription.getMaxReservationsPerMonth())
                .maxConsecutiveHours(subscription.getMaxConsecutiveHours())
                .advanceBookingDays(subscription.getAdvanceBookingDays())
                .canBookMeetingRooms(subscription.getCanBookMeetingRooms())
                .canAccessPremiumAreas(subscription.getCanAccessPremiumAreas())
                .isActive(subscription.getIsActive())
                .createdAt(subscription.getCreatedDate())
                .updatedAt(subscription.getLastModifiedDate())
                .build();
    }

    /**
     * Get features as a list
     */
    public List<String> getFeaturesList() {
        if (features == null || features.trim().isEmpty()) {
            return List.of();
        }
        return Arrays.asList(features.split(","));
    }

    /**
     * Get formatted price with currency
     */
    public String getFormattedPrice() {
        return price + " " + currency;
    }
}
