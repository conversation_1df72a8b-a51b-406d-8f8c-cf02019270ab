package com.workeem.workeem_api.multitenancy.web;

import com.workeem.workeem_api.multitenancy.tenant.application.TenantManagementService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/tenants")
@RequiredArgsConstructor
@CrossOrigin(origins = "http://localhost:4200")
public class TenantApiController {

    private final TenantManagementService tenantManagementService;

    /**
     * Vérifier si un tenant existe
     */
    @GetMapping("/verify/{tenantId}")
    public ResponseEntity<Map<String, Object>> verifyTenant(@PathVariable String tenantId) {
        boolean exists = tenantManagementService.tenantExists(tenantId);

        Map<String, Object> response = Map.of(
            "exists", exists,
            "tenantId", tenantId
        );

        return ResponseEntity.ok(response);
    }

    @PostMapping
    public ResponseEntity<Void> createTenant(
            @RequestParam String tenantId,
            @RequestParam String dbName,
            @RequestParam(required = false) String urlPrefix,
            @RequestParam String username,
            @RequestParam String password,
            @RequestParam(required = false) String driverClass) {
        tenantManagementService.createTenant(tenantId, dbName, urlPrefix, username, password, driverClass);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
