package com.workeem.workeem_api.multitenancy.tenant.application;

public interface TenantManagementService {

    void createTenant(
            String tenantId,
            String dbName,
            String urlPrefix,
            String username,
            String password,
            String driverClass
    );

    /**
     * Vérifier si un tenant existe
     */
    boolean tenantExists(String tenantId);

}