package com.workeem.workeem_api.multitenancy.master.domain;

import java.io.Serializable;

import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.*;


@Entity
@Table(name = "tenants")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Getter
@Setter
public class Tenant implements Serializable {

    @Id
    @Size(max = 30)
    @Column(name = "tenant_id", nullable = false, length = 30)
    private String tenantId;

    @Size(max = 50)
    @Column(name = "db_name", nullable = false, length = 50)
    private String dbName;

    @Size(max = 100)
    @Column(name = "url", nullable = false, length = 100)
    private String url;

    @Size(max = 50)
    @Column(name = "username", nullable = false, length = 50)
    private String username;

    @Size(max = 100)
    @Column(name = "password", nullable = false, length = 100)
    private String password;

    @Size(max = 100)
    @Column(name = "driver_class", nullable = false)
    private String driverClass;
}
