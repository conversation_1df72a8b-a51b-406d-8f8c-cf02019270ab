package com.workeem.workeem_api.shared.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * DTO générique pour les réponses paginées
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageResponseDto<T> {

    private List<T> content; // Contenu de la page
    private int page; // Page courante (0-based)
    private int size; // Taille de la page
    private long totalElements; // Nombre total d'éléments
    private int totalPages; // Nombre total de pages
    private boolean first; // Est-ce la première page ?
    private boolean last; // Est-ce la dernière page ?
    private boolean empty; // La page est-elle vide ?
    private int numberOfElements; // Nombre d'éléments dans cette page

    /**
     * Crée un PageResponseDto à partir d'un objet Page de Spring Data
     */
    public static <T> PageResponseDto<T> fromPage(Page<T> page) {
        return PageResponseDto.<T>builder()
                .content(page.getContent())
                .page(page.getNumber())
                .size(page.getSize())
                .totalElements(page.getTotalElements())
                .totalPages(page.getTotalPages())
                .first(page.isFirst())
                .last(page.isLast())
                .empty(page.isEmpty())
                .numberOfElements(page.getNumberOfElements())
                .build();
    }

    /**
     * Crée un PageResponseDto vide
     */
    public static <T> PageResponseDto<T> empty() {
        return PageResponseDto.<T>builder()
                .content(List.of())
                .page(0)
                .size(0)
                .totalElements(0)
                .totalPages(0)
                .first(true)
                .last(true)
                .empty(true)
                .numberOfElements(0)
                .build();
    }
}
