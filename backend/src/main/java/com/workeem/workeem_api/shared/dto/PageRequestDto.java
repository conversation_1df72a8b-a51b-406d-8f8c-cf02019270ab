package com.workeem.workeem_api.shared.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

/**
 * DTO pour les paramètres de pagination
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageRequestDto {

    @Builder.Default
    private int page = 0; // Page courante (0-based)
    
    @Builder.Default
    private int size = 10; // Taille de la page
    
    private String sortBy; // Champ de tri
    
    @Builder.Default
    private String sortDirection = "ASC"; // Direction du tri (ASC/DESC)

    /**
     * Convertit ce DTO en objet Pageable de Spring Data
     */
    public Pageable toPageable() {
        Sort sort = Sort.unsorted();
        
        if (sortBy != null && !sortBy.trim().isEmpty()) {
            Sort.Direction direction = "DESC".equalsIgnoreCase(sortDirection) 
                ? Sort.Direction.DESC 
                : Sort.Direction.ASC;
            sort = Sort.by(direction, sortBy);
        }
        
        return PageRequest.of(page, size, sort);
    }

    /**
     * Valide les paramètres de pagination
     */
    public void validate() {
        if (page < 0) {
            this.page = 0;
        }
        if (size <= 0 || size > 100) {
            this.size = 10;
        }
    }
}
